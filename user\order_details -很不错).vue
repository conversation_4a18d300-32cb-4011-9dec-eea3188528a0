<template>
	<view class="page">
		<u-modal :show="show" title="取消订单" content='确认取消该订单吗' showCancelButton @cancel="show= false" @confirm="confirm"></u-modal>
		<u-modal :show="afterSalesShow" title="申请售后" showCancelButton @cancel="afterSalesShow= false" @confirm="submitAfterSales">
			<view class="after-sales-input">
				<textarea v-model="afterSalesValue" placeholder="请输入售后内容" class="after-sales-textarea"></textarea>
			</view>
		</u-modal>

		<!-- 师傅信息卡片 -->
		<view class="coach-card" v-if="info.coachInfo">
			<view class="coach-info">
				<view class="coach-left">
					<view class="coach-name-row">
						<view class="coach-name">{{info.coachInfo.coachName}}</view>
						<view class="coach-label" v-if="info.coachInfo.label_name">{{info.coachInfo.label_name}}</view>
					</view>
					<view class="coach-time">{{info.createTime}}</view>
				</view>
				<view class="coach-avatar">
					<image :src="info.coachInfo.selfImg" mode="aspectFill"></image>
				</view>
			</view>
			<view v-if="![-1, 1, -2, -3].includes(info.payType)" class="call-btn" @click="call">
				<view class="call-icon">
					<uni-icons type="phone-filled" size="16" color="#fff"></uni-icons>
				</view>
				<text class="call-text">联系师傅</text>
			</view>
		</view>

		<!-- 售后按钮 -->
		<view v-if="info.payType===7" class="after-sales-btn" @click="openAfterSales">
			<uni-icons type="service" size="18" color="#2E80FE"></uni-icons>
			<text>申请售后</text>
		</view>
		<!-- 订单进度 -->
		<view class="progress-card">
			<view class="progress-title">
				<uni-icons type="list" size="18" color="#2E80FE"></uni-icons>
				<text>订单进度</text>
			</view>
			<u-steps current="4" direction="column" activeColor="#2E80FE">
				<u-steps-item :title="item.title" :desc="item.desc" v-for="(item,index) in list" :key="index">
					<view class="step-icon" slot="icon">
						<view class="step-dot">
							<u-icon name="checkbox-mark" color="#ffffff" size="12"></u-icon>
						</view>
					</view>
				</u-steps-item>
			</u-steps>
		</view>

		<!-- 信息卡片 -->
		<view class="info-card" v-for="(item,index) in infoList" :key="index">
			<view class="card-header">
				<view class="card-title">
					<uni-icons :type="getCardIcon(item.title)" size="18" :color="getCardColor(item.title)"></uni-icons>
					<text>{{item.title}}</text>
				</view>
			</view>

			<!-- 服务信息特殊处理，显示商品卡片布局 -->
			<view v-if="item.title === '服务信息'" class="service-content">
				<!-- 基本服务信息 -->
				<view class="basic-info">
					<view class="info-row" v-for="(newItem,newIndex) in item.children.filter(child => child.isBasicInfo)" :key="newIndex">
						<view class="info-label">{{newItem.name}}</view>
						<view class="info-value">{{newItem.value}}</view>
					</view>
				</view>

				<!-- 商品列表 -->
				<view class="goods-section">
					<view class="section-title">服务商品</view>
					<view class="goods-list">
						<view class="goods-item" v-for="(goods, goodsIndex) in info.orderGoods" :key="goodsIndex">
							<view class="goods-layout">
								<image class="goods-img" :src="goods.goodsCover" mode="aspectFill"></image>
								<view class="goods-info">
									<view class="goods-title">{{goods.goodsName}}</view>
									<view class="goods-specs">
										<view class="spec-item" v-for="setting in goods.priceSetting" :key="setting.id">
											<text class="spec-text" v-if="setting.val && setting.val !== '' && setting.inputType !== 2">{{setting.problemDesc}}：{{setting.val}}</text>
										</view>
									</view>
								</view>
								<view class="goods-price-info">
									<view class="price" v-if="goods.price > 0">¥{{goods.price}}</view>
									<view class="quantity">×{{goods.num}}</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 其他信息卡片内容 -->
			<view v-else class="card-content">
				<view class="info-row" v-for="(newItem,newIndex) in item.children" :key="newIndex">
					<view class="info-label">{{newItem.name}}</view>
					<view class="info-value" :class="{'price-highlight': newItem.name.includes('费用') || newItem.name.includes('优惠')}">{{newItem.value}}</view>
				</view>
			</view>
		</view>
		<!-- 操作按钮 -->
		<view class="action-buttons" v-if="info.payType<=1 && info.payType != -1">
			<view class="cancel-btn" @click="cancelOrder">
				<uni-icons type="close" size="16" color="#FF4444"></uni-icons>
				<text>取消订单</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				show: false,
				afterSalesShow: false,
				afterSalesValue: '',
				info: {},
				list: [{
					title: '订单已生成',
					desc: '预定成功，将尽快为主人派单'
				}],
				infoList: [{
						title: '预约信息',
						children: []
					},
					{
						title: "服务信息",
						children: []
					},
					{
						title: "费用明细",
						children: []
					},
					{
						title: "优惠明细",
						children: []
					},
					{
						title: "下单明细",
						children: []
					}
				],
				id: ''
			}
		},
		methods: {
			// 获取卡片图标
			getCardIcon(title) {
				const iconMap = {
					'预约信息': 'calendar',
					'服务信息': 'gear',
					'费用明细': 'wallet',
					'优惠明细': 'gift',
					'下单明细': 'list'
				};
				return iconMap[title] || 'info';
			},
			// 获取卡片颜色
			getCardColor(title) {
				const colorMap = {
					'预约信息': '#2E80FE',
					'服务信息': '#00B26A',
					'费用明细': '#FF6B35',
					'优惠明细': '#FF4444',
					'下单明细': '#8B5CF6'
				};
				return colorMap[title] || '#666666';
			},
			confirm() {
				this.$api.service.cancelOrder({id: this.info.id}).then(res => {
					uni.showToast({
						icon: 'none',
						title: '取消成功'
					})
					uni.$emit('cancelOr')
					setTimeout(() => {
						uni.navigateBack()
					}, 1000)
				})
			},
			cancelOrder() {
				this.show = true
			},
			openAfterSales() {
				this.afterSalesShow = true
			},
			submitAfterSales() {
				if (!this.afterSalesValue.trim()) {
					uni.showToast({
						icon: 'none',
						title: '请输入售后内容'
					})
					return
				}
				// Here you would typically call an API to submit the after-sales request
				this.$api.service.submitAfterSales({
					orderId: this.id,
					remark: this.afterSalesValue
				}).then(res => {
					console.log(res)
					if(res.code==="-1"){
						uni.showToast({
							icon: 'none',
							title: res.msg
						})
					}else{
						uni.showToast({
							icon: 'none',
							title: '售后申请提交成功'
						})
						this.afterSalesShow = false
						this.afterSalesValue = ''
					}
					
				}).catch(err => {
					uni.showToast({
						icon: 'none',
						title: '提交失败，请重试'
					})
				})
			},
			call() {
				if (!this.info.coachInfo.mobile || this.info.coachInfo.mobile.includes('*')) {
				    uni.showToast({
				      title: '无法拨打电话，号码不可用',
				      icon: 'none'
				    });
				    return;
				  }
				uni.makePhoneCall({
					phoneNumber: this.info.coachInfo.mobile
				});
			},
			getInfo() {
				this.$api.service.orderdet(this.id).then(res => {
					console.log(res)
					this.info = res.data
					if (this.info.coachInfo) {
						this.list.push({
							title: '订单已派单',
							desc: `订单交给${this.info.coachInfo.coachName}，将督促师傅尽快跟您联系`
						})
					}
					if (this.info.payType == 7) {
						this.list.push({
							title: '订单完成',
							desc: `订单已完成`
						})
					} else if (this.info.payType == -1) {
						this.list.push({
							title: '订单取消',
							desc: `订单已取消`
						})
					}
					this.infoList[0].children = [{
							name: '预约时间',
							value: this.info.startTime
						},
						{
							name: '服务地址',
							value: this.info.addressInfo.address + this.info.addressInfo.addressInfo + this.info.addressInfo.houseNumber
						},
						// {
						// 	name: '预约服务',
						// 	value: this.info.orderGoods[0].goodsName
						// }
					]
					// 构建服务信息，只包含基本信息（服务类型、师傅信息）
					// 商品详情将在模板中直接渲染
					this.infoList[1].children = [
						{
							name: '服务类型',
							value: this.info.type == 0 ? '一口价' : '报价',
							isBasicInfo: true
						},
						{
							name: '服务师傅',
							value: this.info.coachInfo ? this.info.coachInfo.coachName : '',
							isBasicInfo: true
						}
					];
					this.infoList[2].children = [{
						name: '服务费用',
						value: this.info.payPrice + '元'
					}]
					this.infoList[3].children = [{
						name: '优惠券',
						value: this.info.couponInfo ? '-' + this.info.couponInfo.discount + '元' : '无'
					}]
					this.infoList[4].children = [{
							name: '订单号码',
							value: this.info.orderCode
						},
						{
							name: '下单时间',
							value: this.info.createTime 
						}
					]
				})
			}
		},
		onUnload() {
			let pageArr = getCurrentPages()
			let length = pageArr.length
			if (pageArr[length - 2].route == "/pages/order_success") {
				uni.navigateBack({
					delta: 9
				})
			}
		},
		onLoad(options) {
			this.id = options.id
			this.getInfo()
		}
	}
</script>

<style scoped lang="scss">
	.page {
		background: linear-gradient(180deg, #F5F7FA 0%, #F8F9FB 100%);
		min-height: 100vh;
		padding: 20rpx;

		// 师傅信息卡片
		.coach-card {
			background: #FFFFFF;
			border-radius: 20rpx;
			margin-bottom: 20rpx;
			box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
			overflow: hidden;

			.coach-info {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 32rpx;
				border-bottom: 1rpx solid #F0F0F0;

				.coach-left {
					flex: 1;

					.coach-name-row {
						display: flex;
						align-items: center;
						margin-bottom: 16rpx;

						.coach-name {
							font-size: 36rpx;
							font-weight: 600;
							color: #1A1A1A;
							margin-right: 16rpx;
						}

						.coach-label {
							background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
							color: #FFFFFF;
							font-size: 22rpx;
							font-weight: 500;
							padding: 6rpx 12rpx;
							border-radius: 12rpx;
						}
					}

					.coach-time {
						font-size: 26rpx;
						color: #8E8E93;
					}
				}

				.coach-avatar {
					image {
						width: 88rpx;
						height: 88rpx;
						border-radius: 50%;
						border: 3rpx solid #F0F0F0;
					}
				}
			}

			.call-btn {
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 24rpx;
				background: linear-gradient(135deg, #2E80FE 0%, #1E6FFF 100%);

				.call-icon {
					width: 36rpx;
					height: 36rpx;
					background: rgba(255, 255, 255, 0.2);
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
					margin-right: 12rpx;
				}

				.call-text {
					font-size: 30rpx;
					font-weight: 500;
					color: #FFFFFF;
				}
			}
		}

		// 售后按钮
		.after-sales-btn {
			display: flex;
			align-items: center;
			justify-content: center;
			background: #FFFFFF;
			border-radius: 16rpx;
			padding: 24rpx;
			margin-bottom: 20rpx;
			box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
			border: 2rpx solid #2E80FE;

			text {
				font-size: 30rpx;
				font-weight: 500;
				color: #2E80FE;
				margin-left: 12rpx;
			}
		}

		// 售后输入框
		.after-sales-input {
			padding: 32rpx;

			.after-sales-textarea {
				width: 100%;
				min-height: 200rpx;
				padding: 24rpx;
				border: 2rpx solid #E9E9E9;
				border-radius: 16rpx;
				font-size: 28rpx;
				color: #333333;
				background: #FAFAFA;
				box-sizing: border-box;
			}
		}

		// 进度卡片
		.progress-card {
			background: #FFFFFF;
			border-radius: 20rpx;
			margin-bottom: 20rpx;
			padding: 32rpx;
			box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);

			.progress-title {
				display: flex;
				align-items: center;
				margin-bottom: 32rpx;

				text {
					font-size: 32rpx;
					font-weight: 600;
					color: #1A1A1A;
					margin-left: 12rpx;
				}
			}

			.step-icon {
				.step-dot {
					width: 32rpx;
					height: 32rpx;
					background: linear-gradient(135deg, #2E80FE 0%, #1E6FFF 100%);
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
					box-shadow: 0 2rpx 8rpx rgba(46, 128, 254, 0.3);
				}
			}

			::v-deep .u-steps-item__line {
				background: linear-gradient(180deg, #2E80FE 0%, #E9E9E9 100%) !important;
			}
		}

		// 信息卡片
		.info-card {
			background: #FFFFFF;
			border-radius: 20rpx;
			margin-bottom: 20rpx;
			box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
			overflow: hidden;

			.card-header {
				padding: 32rpx 32rpx 0;

				.card-title {
					display: flex;
					align-items: center;
					margin-bottom: 24rpx;

					text {
						font-size: 32rpx;
						font-weight: 600;
						color: #1A1A1A;
						margin-left: 12rpx;
					}
				}
			}

			.card-content {
				padding: 0 32rpx 32rpx;

				.info-row {
					display: flex;
					align-items: center;
					justify-content: space-between;
					padding: 20rpx 0;
					border-bottom: 1rpx solid #F5F5F5;

					&:last-child {
						border-bottom: none;
					}

					.info-label {
						font-size: 28rpx;
						color: #8E8E93;
						flex-shrink: 0;
					}

					.info-value {
						font-size: 28rpx;
						color: #1A1A1A;
						font-weight: 500;
						text-align: right;
						flex: 1;
						margin-left: 20rpx;

						&.price-highlight {
							color: #FF6B35;
							font-weight: 600;
						}
					}
				}
			}

			// 服务信息特殊布局
			.service-content {
				padding: 0 32rpx 32rpx;

				.basic-info {
					.info-row {
						display: flex;
						align-items: center;
						justify-content: space-between;
						padding: 20rpx 0;
						border-bottom: 1rpx solid #F5F5F5;

						.info-label {
							font-size: 28rpx;
							color: #8E8E93;
						}

						.info-value {
							font-size: 28rpx;
							color: #1A1A1A;
							font-weight: 500;
						}
					}
				}

				.goods-section {
					margin-top: 32rpx;

					.section-title {
						font-size: 30rpx;
						font-weight: 600;
						color: #1A1A1A;
						margin-bottom: 20rpx;
						padding-bottom: 16rpx;
						border-bottom: 2rpx solid #F0F0F0;
					}

					.goods-list {
						.goods-item {
							margin-bottom: 24rpx;
							padding: 24rpx;
							background: #FAFBFC;
							border-radius: 16rpx;
							border: 1rpx solid #F0F0F0;

							&:last-child {
								margin-bottom: 0;
							}

							.goods-layout {
								display: flex;
								align-items: flex-start;

								.goods-img {
									width: 120rpx;
									height: 120rpx;
									border-radius: 16rpx;
									flex-shrink: 0;
									margin-right: 20rpx;
									border: 2rpx solid #F5F5F5;
								}

								.goods-info {
									flex: 1;
									min-width: 0;

									.goods-title {
										font-size: 32rpx;
										font-weight: 600;
										color: #1A1A1A;
										margin-bottom: 16rpx;
										line-height: 1.4;
									}

									.goods-specs {
										.spec-item {
											margin-bottom: 8rpx;

											.spec-text {
												font-size: 26rpx;
												color: #666666;
												line-height: 1.4;
												background: #FFFFFF;
												padding: 8rpx 12rpx;
												border-radius: 8rpx;
												display: inline-block;
												margin-right: 8rpx;
												margin-bottom: 8rpx;
											}
										}
									}
								}

								.goods-price-info {
									flex-shrink: 0;
									text-align: right;
									display: flex;
									flex-direction: column;
									align-items: flex-end;
									justify-content: space-between;
									min-height: 120rpx;

									.price {
										font-size: 36rpx;
										font-weight: 700;
										color: #FF6B35;
										margin-bottom: 8rpx;
									}

									.quantity {
										font-size: 28rpx;
										color: #8E8E93;
										background: #FFFFFF;
										padding: 6rpx 12rpx;
										border-radius: 12rpx;
										border: 1rpx solid #E9E9E9;
									}
								}
							}
						}
					}
				}
			}
		}

		// 操作按钮区域
		.action-buttons {
			padding: 32rpx 0;
			display: flex;
			justify-content: center;

			.cancel-btn {
				display: flex;
				align-items: center;
				justify-content: center;
				background: #FFFFFF;
				border: 2rpx solid #FF4444;
				border-radius: 50rpx;
				padding: 20rpx 40rpx;
				box-shadow: 0 4rpx 20rpx rgba(255, 68, 68, 0.15);

				text {
					font-size: 30rpx;
					font-weight: 500;
					color: #FF4444;
					margin-left: 8rpx;
				}

				&:active {
					transform: scale(0.98);
					transition: transform 0.1s;
				}
			}
	}
	}
</style>