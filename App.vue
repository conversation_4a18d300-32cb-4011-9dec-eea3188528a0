<script>
	import $api from "@/api/index.js"
	import $store from "@/store/index.js"
	export default {
		// globalData: {
		//     primaryColor: '#A40035', // Default fallback, matching your sidebar’s color
		//   },
		data() {
			return {

			}
		},

		async mounted() {
			// #ifdef H5
			if (typeof window.entryUrl === 'undefined' || window.entryUrl === '') {
				window.entryUrl = window.location.href.split('#')[0]
			}
			if (window.location.href.indexOf('?#') < 0) {
				window.location.href = window.location.href.replace("#", "?#");
			}
			// #endif
			console.log('App mounted')
		},
	
		async onLaunch() {
	
	
			const shiInfo = wx.getStorageSync('shiInfo');

			if (shiInfo) {
				// 如果 shiInfo 存在，跳转到 shifu/Receiving 页面
				wx.redirectTo({
					url: '/shifu/Receiving'
				});
			} else {
				
			}
			let configInfo = uni.getStorageSync('configInfo') || ''
			if (configInfo) {
				$store.commit('updateConfigItem', {
					key: 'configInfo',
					val: configInfo
				})
			}

			let arr = ['autograph', 'userInfo', 'location', 'appLogin']
			arr.map(key => {
				let val = uni.getStorageSync(key) || ''
				if (val) {
					$store.commit('updateUserItem', {
						key,
						val
					})
				}
			})
			let commonOptions = $store.state.user.commonOptions
			let {
				channel_id = 0
			} = commonOptions
			if (channel_id) {
				commonOptions.channel_id = 0
				$store.commit('updateUserItem', {
					key: 'commonOptions',
					val: commonOptions
				})
			}
			let {
				primaryColor = ''
			} = $store.state.config.configInfo
			if (primaryColor) return
			await this.getBaseConfig()
		},
		async onShow() {
			console.log('App Show')
	
		},
		onHide() {
			console.log('App Hide')
		},
		methods: {
			async getBaseConfig() {
				let config = await $api.base.getConfig()
				if (!config.primaryColor) {
					config.primaryColor = '#A40035'
				}
				if (!config.subColor) {
					config.subColor = '#F1C06B'
				}
				let configInfo = Object.assign($store.state.config.configInfo, config)
				$store.commit('updateConfigItem', {
					key: 'configInfo',
					val: configInfo
				})
			}
		}
	}
</script>

<style lang="scss">
	@import "uview-ui/index.scss";
	@import "/styles/index.wxss";

	/* #ifdef H5 */
	uni-page-head {
		display: none;
	}

	/* #endif */
	page {
		font-size: 28rpx;
		color: #222;
		line-height: 1.5;
		background: #fff;
		font-family: 'MyFont';
	}

	input {
		// font-family: PingFangSC-Medium, PingFang SC, -apple-system-font, Helvetica Neue, Helvetica, sans-serif;
	}

	input::-webkit-input-placeholder {
		/* WebKit browsers */
		color: #A9A9A9;
	}

	input:-moz-placeholder {
		/* Mozilla Firefox 4 to 18 */
		color: #A9A9A9;
	}

	input::-moz-placeholder {
		/* Mozilla Firefox 19+ */
		color: #A9A9A9;
	}

	input:-ms-input-placeholder {
		/* Internet Explorer 10+ */
		color: #A9A9A9;
	}

	view {
		box-sizing: border-box;
	}

	image {
		display: block;
	}

	/*隐藏滚动条*/
	::-webkit-scrollbar {
		width: 0;
		height: 0;
		color: transparent;
	}

	/* #ifdef MP-BAIDU */
	.swan-button.swan-button-radius-ios {
		border-radius: 0;
	}

	/* #endif */
</style>