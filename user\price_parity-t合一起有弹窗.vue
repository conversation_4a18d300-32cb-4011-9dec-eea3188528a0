<template>
  <view class="page">
    <!-- 服务配置表单部分 -->
    <view class="header">
      <image :src="serviceInfo.cover" mode="scaleToFill"></image>
    </view>
    <view class="content">
      <view class="card">
        <view class="top">
          <view class="title">{{serviceInfo.title}}</view>
          <view class="price" v-if="serviceInfo.servicePriceType !=1">￥{{serviceInfo.price}}</view>
        </view>
        <view class="bottom">
          <view class="left">已选：</view>
          <view class="right">
            <view class="">{{yikoujiaprice}}</view>
            <view class="tag" v-for="(item,index) in chooseArr" :key="index">{{item.name}}</view>
          </view>
        </view>
      </view>
      
      <!-- 选择项 -->
      <view class="chol" v-for="(item,index) in list" :key="index">
        <view class="choose">
          <view class="title"><span v-if="item.isRequired == 1">*</span>{{item.problemDesc}}</view>
          <view class="desc">{{item.problemContent}}</view>
          <view class="cho_box">
            <view class="box_item" v-for="(newItem,newIndex) in item.options" :key="newIndex"
              :style="newItem.choose?'border:2rpx solid #2E80FE;color: #2E80FE;':''"
              @click="chooseOne(index,newIndex,item.inputType)">
              {{newItem.name}}
              <view class="ok" :style="newItem.choose? '' : 'display:none;'">
                <uni-icons type="checkmarkempty" size="8" color="#fff"></uni-icons>
              </view>
            </view>
          </view>
        </view>
        <view class="fg"></view>
      </view>
      
      <!-- 输入框部分 -->
      <view class="chol" v-for="(item, index) in list2" :key="item.id">
        <view class="choose">
          <view class="title"><span v-if="item.isRequired == 1">*</span>{{item.problemDesc}}</view>
          <view class="desc">{{item.problemContent}}</view>
          <view class="input-container" :id="'input-container-' + index">
            <input  
              type="text" 
              v-model="form.data[index + list.length].val" 
              :placeholder="'请输入' + item.problemDesc"
              @focus="handleInputFocus(index)"
              @blur="handleInputBlur"
              @input="handleInput"
              class="form-input"
              cursor-spacing="10"
              confirm-type="done"
              :adjust-position="false"
              :auto-height="false"
            />
          </view>
        </view>
        <view class="fg"></view>
      </view>
      
      <!-- 图片上传部分 -->
      <view class="chol" v-for="(item,index) in list3" :key="index">
        <view class="choose">
          <view class="title"><span v-if="item.isRequired == 1">*</span>{{item.problemDesc}}</view>
          <view class="desc up">{{item.problemContent}}</view>
          <upload @upload="imgUpload" @del="imgUpload"
            :imagelist="form.data[form.data.findIndex(e=>e.serviceId == item.id)].val"
            :imgtype="form.data.findIndex(e=>e.serviceId == item.id)" text="上传图片" :imgsize="3">
          </upload>
        </view>
        <view class="fg"></view>
      </view>
      <view style="height: 300rpx;"></view> 
    </view>
    
    <!-- 底部按钮 -->
    <view class="footer" :style="footerStyle">
      <view class="righ" 
            :class="{ 'submitting': isSubmitting }" 
            @click="showOrderModal">
        {{ isSubmitting ? '提交中...' : '立即下单' }}
      </view>
    </view>

    <!-- 订单确认模态框 -->
    <view class="order-modal" v-if="showModal" @click.self="closeModal">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">确认订单</text>
          <view class="close-btn" @click="closeModal">
            <uni-icons type="close" size="20" color="#666"></uni-icons>
          </view>
        </view>
        
        <scroll-view scroll-y class="modal-body">
          <!-- 地址选择 -->
          <view class="address-section" @click="goToAddress">
            <view class="section-title">
              <uni-icons type="location" size="16" color="#2E80FE"></uni-icons>
              <text>收货地址</text>
            </view>
            <view class="address-content">
              <text class="address-text">{{mrAddress.address || '请选择收货地址'}}</text>
              <text class="contact-info" v-if="mrAddress.address">{{mrAddress.userName}} {{mrAddress.mobile}}</text>
            </view>
            <uni-icons name="arrow-right" color="#999" size="14"></uni-icons>
          </view>

          <!-- 时间选择 -->
          <view class="time-section" @click="showTimeModal = true">
            <view class="section-title">
              <uni-icons type="calendar" size="16" color="#2E80FE"></uni-icons>
              <text>服务时间</text>
            </view>
            <view class="time-content">
              <text class="time-text">{{selectedTimeText}}</text>
            </view>
            <uni-icons name="arrow-right" color="#999" size="14"></uni-icons>
          </view>

          <!-- 服务详情 -->
          <view class="service-section">
            <view class="section-title">
              <uni-icons type="list" size="16" color="#2E80FE"></uni-icons>
              <text>服务详情</text>
            </view>
            <view class="service-item">
              <image :src="serviceInfo.cover" class="service-image"></image>
              <view class="service-info">
                <text class="service-title">{{serviceInfo.title}}</text>
                <view class="service-price">
                  <text class="price-text">￥{{serviceInfo.price}}</text>
                  <u-number-box v-model="serviceQuantity" :min="1" @change="onQuantityChange"></u-number-box>
                </view>
              </view>
            </view>
          </view>

          <!-- 加急选项 -->
          <view class="urgent-section">
            <u-checkbox-group @change="checkboxChange">
              <u-checkbox 
                v-model="isUrgent" 
                name="urgent" 
                shape="circle" 
                label="是否加急"
              ></u-checkbox>
            </u-checkbox-group>
          </view>

          <!-- 备注 -->
          <view class="notes-section">
            <view class="section-title">
              <uni-icons type="compose" size="16" color="#2E80FE"></uni-icons>
              <text>服务备注</text>
            </view>
            <textarea 
              v-model="notes" 
              placeholder="想要额外嘱咐工作人员的可以备注哦~"
              class="notes-input"
            ></textarea>
          </view>
        </scroll-view>

        <!-- 模态框底部 -->
        <view class="modal-footer">
          <view class="total-price">总计：￥{{totalPrice}}</view>
          <view class="action-buttons">
            <view class="add-cart-btn" @click="addToCart">加入购物车</view>
            <view class="submit-btn" @click="submitOrder">立即下单</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 时间选择模态框 -->
    <view class="time-modal" v-if="showTimeModal" @click.self="showTimeModal = false">
      <view class="time-modal-content" @click.stop>
        <view class="time-modal-header">
          <text>选择服务时间</text>
          <view class="close-btn" @click="showTimeModal = false">
            <uni-icons type="close" size="20" color="#666"></uni-icons>
          </view>
        </view>
        
        <!-- 日期选择 -->
        <view class="date-selector">
          <view class="date-item" 
                v-for="(item,index) in dateArr" 
                :key="index"
                :class="{ 'active': currentDate === index }"
                @click="selectDate(index)">
            <text class="date-label">{{item.str}}</text>
            <text class="date-value">{{item.date}}</text>
          </view>
        </view>

        <!-- 时间段选择 -->
        <scroll-view scroll-y class="time-slots">
          <view class="time-grid">
            <view class="time-slot" 
                  v-for="(item, index) in timeArr" 
                  :key="index"
                  :class="{ 
                    'active': currentTime === index,
                    'disabled': item.disabled 
                  }"
                  @click="selectTime(index)">
              {{item.time}}
            </view>
          </view>
        </scroll-view>

        <view class="time-modal-footer">
          <view class="confirm-time-btn" @click="confirmTime">确认时间</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 原有 price_parity 数据
      id: '',
      type: '',
      chooseArr: [],
      list: [],
      list2: [],
      list3: [],
      serviceInfo: {},
      form: { data: [], id: '' },
      btArr: [],
      focusedInputIndex: -1,
      keyboardHeight: 0,
      showprice: false,
      yikoujiaprice: '',
      windowHeight: 0,
      isKeyboardShow: false,
      systemInfo: {},
      scrollTimer: null,
      isSubmitting: false,
      
      // 新增模态框相关数据
      showModal: false,
      showTimeModal: false,
      
      // 订单相关数据
      mrAddress: {},
      serviceQuantity: 1,
      isUrgent: false,
      notes: '',
      
      // 时间选择相关
      dateArr: [],
      timeArr: [
        { disabled: false, time: '08:00-10:00', time1: '08:00:00', time2: '10:00:00' },
        { disabled: false, time: '10:00-12:00', time1: '10:00:00', time2: '12:00:00' },
        { disabled: false, time: '12:00-14:00', time1: '12:00:00', time2: '14:00:00' },
        { disabled: false, time: '14:00-16:00', time1: '14:00:00', time2: '16:00:00' },
        { disabled: false, time: '16:00-18:00', time1: '16:00:00', time2: '18:00:00' },
        { disabled: false, time: '18:00-20:00', time1: '18:00:00', time2: '20:00:00' }
      ],
      currentDate: 0,
      currentTime: -1,
      week: ["周日", "周一", "周二", "周三", "周四", "周五", "周六"]
    }
  },
  
  computed: {
    footerStyle() {
      return {
        bottom: this.isKeyboardShow ? this.keyboardHeight + 'px' : '0px'
      }
    },
    
    selectedTimeText() {
      if (this.currentDate >= 0 && this.currentTime >= 0) {
        const dateStr = this.dateArr[this.currentDate]?.date + '(' + this.dateArr[this.currentDate]?.str + ')';
        const timeStr = this.timeArr[this.currentTime]?.time;
        return `${dateStr} ${timeStr}`;
      }
      return '请选择服务时间';
    },
    
    totalPrice() {
      return (this.serviceInfo.price * this.serviceQuantity).toFixed(2);
    }
  },

  methods: {
    // 原有 price_parity 方法
    handleInputFocus(index) {
      this.focusedInputIndex = index;
      this.isKeyboardShow = true;
      this.scrollToInput(index);
    },

    handleInputBlur() {
      this.focusedInputIndex = -1;
      this.isKeyboardShow = false;
      this.keyboardHeight = 0;
    },

    handleInput(e) {
      console.log('输入内容:', e.detail.value);
    },

    scrollToInput(index) {
      // 简化的滚动逻辑
      const query = uni.createSelectorQuery().in(this);
      query.select(`#input-container-${index}`).boundingClientRect();
      query.exec((res) => {
        if (res && res[0]) {
          uni.pageScrollTo({
            scrollTop: res[0].top - 200,
            duration: 300
          });
        }
      });
    },

    imgUpload(e) {
      let { imagelist, imgtype } = e;
      let newFormData = [...this.form.data];
      newFormData[imgtype] = {
        ...newFormData[imgtype],
        val: [...imagelist]
      };
      this.$set(this.form, 'data', newFormData);
    },

    chooseOne(i, j, inputType) {
      this.list[i].options[j].choose = !this.list[i].options[j].choose;
      if (inputType == 3) {
        this.list[i].options.forEach((item, index) => {
          if (index == j) return;
          item.choose = false;
        });
      }

      this.chooseArr = [];
      this.list.forEach(item => {
        item.options.forEach(tem => {
          if (tem.choose) {
            this.chooseArr.push(tem);
          }
        });
      });
    },

    // 新增模态框相关方法
    showOrderModal() {
      if (this.isSubmitting) return;

      // 验证必填项
      if (!this.validateForm()) {
        return;
      }

      this.showModal = true;
    },

    closeModal() {
      this.showModal = false;
      this.showTimeModal = false;
    },

    validateForm() {
      let copy_form = JSON.parse(JSON.stringify(this.form));
      this.chooseArr.forEach(item => {
        copy_form.data[copy_form.data.findIndex(e => e.serviceId == item.serviceId)].val.push(item.name);
      });

      let isValid = true;
      copy_form.data.forEach(item => {
        let index = this.btArr.findIndex(e => e == item.serviceId);
        if (index != -1 && (item.val == '' || (Array.isArray(item.val) && item.val.length === 0))) {
          uni.showToast({
            icon: 'none',
            title: '请填写完整后提交',
            duration: 1500
          });
          isValid = false;
          return;
        }
      });

      return isValid;
    },

    // 地址选择
    goToAddress() {
      uni.navigateTo({
        url: '../user/address'
      });
    },

    // 时间选择相关方法
    selectDate(index) {
      this.currentDate = index;
      this.currentTime = -1;
      this.updateTimeAvailability(index);
    },

    selectTime(index) {
      if (this.timeArr[index].disabled) {
        uni.showToast({
          icon: 'none',
          title: '该时间段不可选择'
        });
        return;
      }
      this.currentTime = index;
    },

    confirmTime() {
      if (this.currentTime === -1) {
        uni.showToast({
          icon: 'none',
          title: '请选择时间段'
        });
        return;
      }
      this.showTimeModal = false;
    },

    updateTimeAvailability(dateIndex) {
      if (dateIndex === 0) {
        const now = new Date();
        const currentHour = now.getHours();

        this.timeArr.forEach(item => {
          const timeStart = parseInt(item.time1.split(':')[0]);
          item.disabled = currentHour >= timeStart;
        });
      } else {
        this.timeArr.forEach(item => {
          item.disabled = false;
        });
      }
    },

    // 数量变化
    onQuantityChange(value) {
      this.serviceQuantity = value;
    },

    // 加急选择
    checkboxChange(e) {
      this.isUrgent = e.includes('urgent');
    },

    // 加入购物车
    addToCart() {
      if (!this.validateOrderForm()) return;

      this.isSubmitting = true;
      this.$api.service.addtocar({
        serviceId: this.id,
        num: this.serviceQuantity
      }).then(res => {
        uni.showToast({
          icon: 'success',
          title: '加入成功'
        });
        setTimeout(() => {
          this.isSubmitting = false;
          this.closeModal();
          uni.redirectTo({
            url: '../user/order'
          });
        }, 1000);
      }).catch(err => {
        this.isSubmitting = false;
        uni.showToast({
          icon: 'none',
          title: '加入购物车失败'
        });
      });
    },

    // 提交订单
    submitOrder() {
      if (!this.validateOrderForm()) return;

      this.isSubmitting = true;

      // 构建订单数据
      const orderData = this.buildOrderData();

      this.$api.service.subOrder(orderData).then(res => {
        if (res.code === '200') {
          uni.showToast({
            icon: 'success',
            title: '提交成功'
          });
          setTimeout(() => {
            this.isSubmitting = false;
            this.closeModal();
            uni.navigateTo({
              url: `../user/wait_price?id=${this.id}`
            });
          }, 500);
        } else {
          this.isSubmitting = false;
          uni.showToast({
            icon: 'none',
            title: res.msg || '提交失败'
          });
        }
      }).catch(err => {
        this.isSubmitting = false;
        uni.showToast({
          icon: 'none',
          title: '提交失败，请重试'
        });
      });
    },

    validateOrderForm() {
      if (!this.mrAddress.id) {
        uni.showToast({
          icon: 'none',
          title: '请选择收货地址'
        });
        return false;
      }

      if (this.currentTime === -1) {
        uni.showToast({
          icon: 'none',
          title: '请选择服务时间'
        });
        return false;
      }

      return true;
    },

    buildOrderData() {
      const selectedDate = this.dateArr[this.currentDate];
      const selectedTime = this.timeArr[this.currentTime];

      const dateStr = selectedDate.fullDate;
      const startTimeStr = `${dateStr} ${selectedTime.time1}`;
      const endTimeStr = `${dateStr} ${selectedTime.time2}`;

      const startTimestamp = new Date(startTimeStr).getTime() / 1000;
      const endTimestamp = new Date(endTimeStr).getTime() / 1000;

      return {
        type: this.type,
        addressId: this.mrAddress.id,
        serviceId: this.id,
        urgent: this.isUrgent ? 1 : 0,
        num: this.serviceQuantity,
        startTime: startTimestamp,
        endTime: endTimestamp,
        text: this.notes
      };
    },

    // 数据获取方法
    async getpzinfo() {
      await this.$api.service.getPz({
        id: this.id,
        type: this.type
      }).then(ress => {
        let res = ress.data;
        res.forEach(item => {
          if (item.isRequired == 1) {
            this.btArr.push(item.id);
          }
          item.options = JSON.parse(item.options);
          item.options = item.options.map(e => {
            return {
              serviceId: item.id,
              name: e,
              choose: false
            };
          });
        });

        this.list = res.filter(item => item.inputType == 3 || item.inputType == 4);
        this.list.forEach((newItem, newIndex) => {
          this.form.data.push({
            "serviceId": newItem.id,
            "settingId": this.id,
            "val": []
          });
        });

        this.list2 = res.filter(item => item.inputType == 1);
        this.list2.forEach((newItem, newindex) => {
          this.form.data.push({
            "serviceId": newItem.id,
            "settingId": this.id,
            "val": ''
          });
        });

        this.list3 = res.filter(item => item.inputType == 2);
        this.list3.forEach((newItem, newindex) => {
          this.form.data.push({
            "serviceId": newItem.id,
            "settingId": this.id,
            "val": []
          });
        });
      });
    },

    async getInfo() {
      await this.$api.service.getserviceInfo(this.id).then(res => {
        if (res.data.price !== 0) {
          this.yikoujiaprice = res.data.price;
          this.showprice = true;
        }
        this.showprice = false;
        this.serviceInfo = res.data;
      });
    },

    async getdefultaddress() {
      try {
        let res = await this.$api.service.getaddressDefault();
        this.mrAddress = res.data;
      } catch (err) {
        console.error('Get default address failed:', err);
      }
    },

    getTime() {
      const now = new Date();
      let currentDate = new Date(now);

      for (let i = 0; i < 4; i++) {
        const month = this.addLeadingZero(currentDate.getMonth() + 1);
        const date = this.addLeadingZero(currentDate.getDate());
        const day = currentDate.getDay();
        const year = currentDate.getFullYear();

        this.dateArr.push({
          str: i === 0 ? '今天' : this.week[day],
          date: month + '-' + date,
          fullDate: `${year}-${month}-${date}`
        });

        currentDate.setDate(currentDate.getDate() + 1);
      }

      this.updateTimeAvailability(0);
    },

    addLeadingZero(number) {
      return number < 10 ? '0' + number : number;
    }
  },

  onLoad(options) {
    this.id = options.id;
    this.type = options.type;
    this.form.id = options.id;
    this.getInfo();
    this.getpzinfo();
    this.getdefultaddress();
    this.getTime();

    // 获取系统信息
    uni.getSystemInfo({
      success: (res) => {
        this.systemInfo = res;
        this.windowHeight = res.windowHeight;
      }
    });
  },

  onShow() {
    this.focusedInputIndex = -1;
    this.isKeyboardShow = false;
    this.keyboardHeight = 0;
    this.isSubmitting = false;

    // 监听地址选择
    let that = this;
    uni.$once('chooseAddress', function(e) {
      that.mrAddress = e;
    });
  },

  onHide() {
    if (this.scrollTimer) {
      clearTimeout(this.scrollTimer);
      this.scrollTimer = null;
    }
  },

  onUnload() {
    if (this.scrollTimer) {
      clearTimeout(this.scrollTimer);
      this.scrollTimer = null;
    }
  }
}
</script>

<style scoped lang="scss">
.page {
  min-height: 100vh;
  position: relative;
  padding-bottom: 200rpx;
}

.header {
  width: 750rpx;
  height: 376rpx;
  position: absolute;
  top: -300rpx;
  left: 0;
  z-index: -999;

  image {
    width: 100%;
    height: 100%;
  }
}

.content {
  margin-top: 280rpx;
}

.card {
  margin-left: 32rpx;
  width: 686rpx;
  background: #FFFFFF;
  box-shadow: 0rpx 0rpx 8rpx 2rpx rgba(0, 0, 0, 0.16);
  border-radius: 16rpx;
  padding: 40rpx;

  .top {
    padding-bottom: 40rpx;
    border-bottom: 2rpx solid #F2F3F6;

    .title {
      font-size: 36rpx;
      font-weight: 500;
      color: #171717;
      letter-spacing: 2rpx;
    }

    .price {
      margin-top: 12rpx;
      font-size: 30rpx;
      font-weight: 500;
      color: #E72427;
    }
  }

  .bottom {
    padding-top: 24rpx;
    display: flex;

    .left {
      font-size: 24rpx;
      font-weight: 400;
      color: #999999;
      padding-top: 10rpx;
    }

    .right {
      flex: 1;
      margin-left: 20rpx;
      display: flex;
      flex-wrap: wrap;
      align-items: center;

      .tag {
        width: fit-content;
        height: 44rpx;
        padding: 0 12rpx;
        background: #DCEAFF;
        border-radius: 4rpx;
        font-size: 16rpx;
        font-weight: 400;
        color: #2E80FE;
        line-height: 44rpx;
        text-align: center;
        margin: 10rpx;
      }
    }
  }
}

.chol {
  .choose {
    padding: 40rpx 32rpx;

    .title {
      font-size: 32rpx;
      font-weight: 500;
      color: #333333;

      span {
        color: #E72427;
      }
    }

    .desc {
      margin-top: 20rpx;
      font-size: 24rpx;
      font-weight: 400;
      color: #ADADAD;

      &.up {
        margin-bottom: 40rpx;
      }
    }

    .input-container {
      margin-top: 40rpx;
      position: relative;
      width: 100%;
      min-height: 88rpx;
    }

    .form-input {
      box-sizing: border-box;
      width: 100%;
      height: 88rpx;
      background: #F7F7F7;
      border-radius: 12rpx;
      padding: 0 30rpx;
      font-size: 28rpx;
      line-height: 88rpx;
      border: 2rpx solid transparent;
      transition: all 0.2s ease;

      &:focus {
        background: #fff;
        border-color: #2E80FE;
        box-shadow: 0 0 0 4rpx rgba(46, 128, 254, 0.1);
        outline: none;
      }
    }

    .cho_box {
      margin-top: 20rpx;
      display: flex;
      flex-wrap: wrap;

      .box_item {
        width: fit-content;
        padding: 0 20rpx;
        height: 60rpx;
        background: #FFFFFF;
        border-radius: 4rpx;
        border: 2rpx solid #D8D8D8;
        font-size: 24rpx;
        font-weight: 400;
        color: #ADADAD;
        line-height: 60rpx;
        margin-right: 20rpx;
        margin-bottom: 20rpx;
        position: relative;

        .ok {
          width: 20rpx;
          height: 20rpx;
          position: absolute;
          right: 0;
          bottom: 0;
          background-color: #2E80FE;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }

  .fg {
    width: 750rpx;
    height: 20rpx;
    background: #F3F4F5;
  }
}

.footer {
  padding: 38rpx 32rpx;
  width: 750rpx;
  background: #ffffff;
  box-shadow: 0rpx 0rpx 6rpx 2rpx rgba(193, 193, 193, 0.3);
  position: fixed;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  transition: bottom 0.25s ease;

  .righ {
    width: 690rpx;
    height: 88rpx;
    background: #2e80fe;
    border-radius: 44rpx;
    font-size: 28rpx;
    font-weight: 400;
    color: #ffffff;
    line-height: 88rpx;
    text-align: center;
    transition: all 0.2s ease;

    &.submitting {
      background: #8bb8ff;
      opacity: 0.7;
      pointer-events: none;
    }
  }
}

// 模态框样式
.order-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: flex-end;

  .modal-content {
    width: 100%;
    max-height: 80%;
    background: #fff;
    border-radius: 20rpx 20rpx 0 0;
    display: flex;
    flex-direction: column;

    .modal-header {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 40rpx 32rpx 20rpx;
      border-bottom: 1rpx solid #f0f0f0;
      position: relative;

      .modal-title {
        font-size: 32rpx;
        font-weight: 500;
        color: #171717;
      }

      .close-btn {
        position: absolute;
        right: 32rpx;
        top: 40rpx;
        padding: 10rpx;
      }
    }

    .modal-body {
      flex: 1;
      padding: 0 32rpx;

      .address-section, .time-section {
        display: flex;
        align-items: center;
        padding: 30rpx 0;
        border-bottom: 1rpx solid #f0f0f0;

        .section-title {
          display: flex;
          align-items: center;
          margin-right: 20rpx;

          text {
            margin-left: 10rpx;
            font-size: 28rpx;
            font-weight: 500;
            color: #333;
          }
        }

        .address-content, .time-content {
          flex: 1;

          .address-text, .time-text {
            font-size: 28rpx;
            color: #333;
            display: block;
          }

          .contact-info {
            font-size: 24rpx;
            color: #999;
            margin-top: 10rpx;
            display: block;
          }
        }
      }

      .service-section {
        padding: 30rpx 0;
        border-bottom: 1rpx solid #f0f0f0;

        .section-title {
          display: flex;
          align-items: center;
          margin-bottom: 20rpx;

          text {
            margin-left: 10rpx;
            font-size: 28rpx;
            font-weight: 500;
            color: #333;
          }
        }

        .service-item {
          display: flex;
          align-items: center;

          .service-image {
            width: 120rpx;
            height: 120rpx;
            border-radius: 12rpx;
            margin-right: 20rpx;
          }

          .service-info {
            flex: 1;

            .service-title {
              font-size: 28rpx;
              color: #333;
              display: block;
              margin-bottom: 20rpx;
            }

            .service-price {
              display: flex;
              align-items: center;
              justify-content: space-between;

              .price-text {
                font-size: 28rpx;
                color: #2E80FE;
                font-weight: 500;
              }
            }
          }
        }
      }

      .urgent-section {
        padding: 30rpx 0;
        border-bottom: 1rpx solid #f0f0f0;
      }

      .notes-section {
        padding: 30rpx 0;

        .section-title {
          display: flex;
          align-items: center;
          margin-bottom: 20rpx;

          text {
            margin-left: 10rpx;
            font-size: 28rpx;
            font-weight: 500;
            color: #333;
          }
        }

        .notes-input {
          width: 100%;
          min-height: 120rpx;
          background: #f7f7f7;
          border-radius: 12rpx;
          padding: 20rpx;
          font-size: 26rpx;
          color: #333;
          border: none;
          outline: none;
        }
      }
    }

    .modal-footer {
      padding: 30rpx 32rpx;
      border-top: 1rpx solid #f0f0f0;

      .total-price {
        font-size: 32rpx;
        font-weight: 600;
        color: #E72427;
        text-align: center;
        margin-bottom: 20rpx;
      }

      .action-buttons {
        display: flex;
        gap: 20rpx;

        .add-cart-btn, .submit-btn {
          flex: 1;
          height: 88rpx;
          border-radius: 44rpx;
          font-size: 28rpx;
          font-weight: 500;
          text-align: center;
          line-height: 88rpx;
        }

        .add-cart-btn {
          background: #fff;
          color: #2E80FE;
          border: 2rpx solid #2E80FE;
        }

        .submit-btn {
          background: #2E80FE;
          color: #fff;
        }
      }
    }
  }
}

// 时间选择模态框样式
.time-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 10000;
  display: flex;
  align-items: flex-end;

  .time-modal-content {
    width: 100%;
    max-height: 70%;
    background: #fff;
    border-radius: 20rpx 20rpx 0 0;
    display: flex;
    flex-direction: column;

    .time-modal-header {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 40rpx 32rpx 20rpx;
      border-bottom: 1rpx solid #f0f0f0;
      position: relative;

      text {
        font-size: 32rpx;
        font-weight: 500;
        color: #171717;
      }

      .close-btn {
        position: absolute;
        right: 32rpx;
        top: 40rpx;
        padding: 10rpx;
      }
    }

    .date-selector {
      display: flex;
      justify-content: space-around;
      padding: 30rpx 20rpx;
      border-bottom: 1rpx solid #f0f0f0;

      .date-item {
        text-align: center;
        padding: 20rpx;
        border-radius: 12rpx;
        transition: all 0.2s ease;

        &.active {
          background: #2E80FE;
          color: #fff;
        }

        .date-label {
          font-size: 24rpx;
          display: block;
          margin-bottom: 10rpx;
        }

        .date-value {
          font-size: 28rpx;
          font-weight: 500;
          display: block;
        }
      }
    }

    .time-slots {
      flex: 1;
      padding: 20rpx;

      .time-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 20rpx;

        .time-slot {
          height: 80rpx;
          background: #f7f7f7;
          border-radius: 12rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 26rpx;
          color: #333;
          transition: all 0.2s ease;

          &.active {
            background: #2E80FE;
            color: #fff;
          }

          &.disabled {
            background: #e0e0e0;
            color: #999;
            pointer-events: none;
          }
        }
      }
    }

    .time-modal-footer {
      padding: 30rpx 32rpx;
      border-top: 1rpx solid #f0f0f0;

      .confirm-time-btn {
        width: 100%;
        height: 88rpx;
        background: #2E80FE;
        border-radius: 44rpx;
        font-size: 28rpx;
        font-weight: 500;
        color: #fff;
        text-align: center;
        line-height: 88rpx;
      }
    }
  }
}

/* iOS安全区域适配 */
@supports (bottom: env(safe-area-inset-bottom)) {
  .footer {
    padding-bottom: calc(38rpx + env(safe-area-inset-bottom));
  }

  .modal-footer {
    padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  }

  .time-modal-footer {
    padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  }
}
</style>
