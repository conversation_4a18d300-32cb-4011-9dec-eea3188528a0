<template>
	<view class="page" ref="abc">
		<!-- Modified: Added confirm popup with page load condition -->
		<view class="confirm-popup-overlay" v-if="showConfirmPopup && isPageLoaded">
			<view class="confirm-popup-container">
				<view class="confirm-popup-content">
					<view class="confirm-title">活动规则</view>
					<view class="confirm-text">
						1.本活动仅限安徽省阜阳市临泉县地区用户参与（以系统验证的定位信息或收货地址为准）<br>
						2. 活动时间：{{huodongdata.startTime}}，结束时间{{huodongdata.endTime}}（北京时间），仅限在此时间段内完成支付的订单可参与<br>
						3. 本次活动仅适用于<text style="color:#E8260D ;">挂式空调清洗服务（不含中央空调、柜式空调等其他类型）</text>，服务包含标准内机清洁<br>
						4.
						每位用户限参与{{huodongdata.maxCount}}次（按用户手机号、支付账号和设备信息识别），服务排期将按支付时间顺序安排，支付成功后服务师傅将在48小时内跟您联系，请注意来电提醒。<br>
						5. 参与活动订单不可使用任何优惠券（专属活动优惠券除外），使用优惠券的订单将自动取消<br>
						6. 禁止通过虚假定位、技术刷单等非正常手段参与，违规订单将视为无效并取消服务资格，用户可在7个工作日内申诉 <br>
						7. 在法律允许范围内，平台保留对活动规则的最终解释及调整权利
					</view>
					<view class="confirm-buttons">
						<view class="confirm-btn cancel" @click="closeConfirmPopup">取消</view>
						<view class="confirm-btn confirm" @click="confirmToHuodongParity">确认</view>
					</view>
				</view>
			</view>
		</view>

		<!-- Added: Login prompt popup -->
		<view class="confirm-popup-overlay" v-if="showLoginPopup">
			<view class="confirm-popup-container">
				<view class="confirm-popup-content">
					<view class="confirm-title">登录提示</view>
					<view class="confirm-text">
						您尚未登录，请先登录以继续参与活动。
					</view>
					<view class="confirm-buttons">
						<view class="confirm-btn cancel" @click="closeLoginPopup">取消</view>
						<view class="confirm-btn confirm" @click="goToLogin">确认</view>
					</view>
				</view>
			</view>
		</view>

		<!-- Modified: Added page load condition -->
		<view class="large-promo-overlay" v-if="showLargePromoPopup && district === '临泉县' && isPageLoaded">
			<view class="large-promo-container">
				<image :src="huodongdata.sharePictures" class="promo-ac" mode="widthFix" />
				<!--  <view class="promo-background-area">
        <image src="../static/images/huodong.png" class="promo-ac" mode="widthFix" />
      </view> -->

				<view class="promo-foreground-area">
					<view class="promo-price">
						<!--  <text class="price-val">{{huodongdata.payPrice}}</text>
          <text class="price-unit">元!!!</text> -->
					</view>
					<!-- <view class="promo-subtitle">
          空调清洗秒杀中!
        </view> -->
				</view>
				<view class="promo-button-area" @click="showConfirm">
					<image src="../static/images/yuyue.png" mode="widthFix" class="button-image" />
					<view class="hand-pointer-animation">
						<image src="/static/images/promo_hand.png" mode="aspectFit" class="hand-pointer-img" />
					</view>
				</view>
				<view class="promo-close-btn" @click="closeLargePromoPopup">
					<uni-icons type="closeempty" color="#fff" size="18"></uni-icons>
				</view>
			</view>
		</view>

		<!-- Modified: Adjusted transition and shrink behavior with page load condition -->
		<view class="activity-popup" v-if="showActivityPopup && district === '临泉县' && isPageLoaded" @click="showConfirm"
			:class="{ 'activity-popup-shrunk': isPopupShrunk }"
			:style="{ transform: isPopupShrunk ? 'translateX(60rpx)' : 'translateX(0)' }">
			<view class="close-btn" @click.stop="closeActivityPopup">×</view>
			<view class="popup-content">
				<view class="popup-text-main">空调清洗</view>
				<image src="../static/images/kongtiao.png" class="popup-header-image" mode="aspectFit"></image>
				<view class="popup-text-price">
					<text class="price-number">{{huodongdata.payPrice}}</text>秒杀
				</view>
				<view class="popup-action-btn">
					立即抢
				</view>
			</view>
		</view>
		<tabbar :cur="0"></tabbar>
		<view class="content">
			<view style="display: flex; align-items: center; justify-content: space-between;">
				<image lazy-load src="../static/images/logo-index.jpg" mode="aspectFit"
					style="width: 50rpx; height: 50rpx; border-radius: 20%;"></image>
				<view class="search_position">
					<uni-icons type="location-filled" size="20"></uni-icons>
					<view class="position">{{ position }}</view>
					<u-icon name="arrow-down-fill" color="#ADADAD" size="8"></u-icon>
					<view class="shu">丨</view>
					<uni-icons type="search" size="20" color="#ADADAD"></uni-icons>
					<input type="text" placeholder="空调维修" @focus="goUrl('/user/search')">
					<view class="btn" @click="goUrl('/user/search')">搜索</view>
				</view>
			</view>
			<view class="img">
				<u-swiper :list="list1" height="108" :lazy-load="true"></u-swiper>
			</view>
			<view class="tag">
				<view class="tag_item"><text>就近师傅</text></view>丨
				<view class="tag_item"><text>准时上门</text></view>丨
				<view class="tag_item"><text>满意为止</text></view>丨
				<!-- <view class="tag_item"><text>30天保修</text></view> -->
				<view class="tag_item" @click="tetxlogin"><text>30天保修</text></view>
			</view>
			<view class="grid">
				<view class="grid-container">
					<view class="grid-item" v-for="(baseListItem, baseListIndex) in baseList" :key="baseListIndex"
						@click="goUrl(baseListItem.link)">
						<image lazy-load :src="baseListItem.img" mode="aspectFit"
							style="width: 96rpx; height: 94rpx; border-radius: 50%;"></image>
						<text class="grid-text">{{ baseListItem.title }}</text>
					</view>
				</view>
			</view>
			<view class="swiper" id="id">
				<span>公<text>告</text></span>
				<view class="shu">丨</view>
				<u-notice-bar :text="text1" url="" direction="column" bgColor="#fff" color="#999" :icon="null"
					fontSize="12"></u-notice-bar>
			</view>
			<view class="welfare">
				<view class="top">
					<view class="left">
						<image lazy-load src="../static/images/4206.png" mode="aspectFit"
							style="width: 46rpx; height: 46rpx;"></image>
						<text>限时福利,先到先得</text>
					</view>
					<!-- Modified: Moved btn back inside welfare.top -->
					<view class="btn" @tap="goUrl('/pages/welfare')">查看更多<u-icon name="play-right-fill" color="#fff"
							size="8"></u-icon>
					</view>
				</view>
				<view class="bottom">
					<image lazy-load src="../static/images/9467.png" mode="aspectFit"
						style="width: 170rpx; height: 224rpx;"></image>
					<view class="right">
						<view class="right_item" v-for="(item, index) in couponList" :key="index">
							<view class="box1"><span>￥</span>{{ item.discount }}元</view>
							<view class="box2">{{ item.full == 0 ? '通用券' : '满减券' }}</view>
							<view class="box3" @click="debouncedGetlingqu(item)"
								:style="item.haveGet == 1 ? 'background:#F2F3F4; color:#ADADAD;' : ''">
								{{ item.haveGet == 1 ? '已领取' : '立即领取' }}
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="service" v-for="(newItem, newIndex) in service" :key="newIndex">
				<view class="head">
					<view class="left">{{ newItem.name }}</view>
					<view class="right" @click="goUrl(`/pages/technician?id=${newItem.id}`)">
						查看更多
						<u-icon name="arrow-right" color="#999999" size="12"></u-icon>
					</view>
				</view>
				<view class="se_main">
					<view class="se_item" v-for="(item, index) in newItem.serviceList" :key="index"
						@click="goUrl(`/user/commodity_details?id=${item.id}`)">
						<image lazy-load :src="item.cover" mode="aspectFit"
							style="width: 290rpx; height: 286rpx; border-radius: 16rpx;"></image>
						<view class="lbox">
							<view class="name">{{ item.title }}</view>
							<view class="baojia">师傅报价</view>
						</view>
					</view>
				</view>
			</view>
			<view style="margin-top: 20rpx;" class="">
				<view class="tips">家庭应急维修 首选今师傅</view>
				<view class="tips2">200万+专业师傅 100万+附近服务网点 覆盖全国城市村镇</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapActions,
		mapMutations
	} from "vuex";
	import tabbar from "@/components/tabbar.vue";
	import {
		debounce
	} from "lodash";
	import { extractTokenFromHeaders } from '@/utils/cookieParser.js';

	export default {
		components: {
			tabbar,
		},
		data() {
			return {
				showLargePromoPopup: true,
				showActivityPopup: true,
				showConfirmPopup: false,
				showLoginPopup: false, // Added: For login prompt popup
				list1: [],
				servicecity:'',
				bannerList: [],
				text1: [],
				huodongdata: '',
				showmsg: '',
				district: '',
				position: "",
				baseList: [],
				service: [],
				couponList: [],
				isLoading: false,
				isRefreshing: false,
				isServiceLoading: false,
				isPopupShrunk: false,
				scrollTimeout: null,
				isPageLoaded: false,
			};
		},
		async onLoad(query) {
			const scene = decodeURIComponent(query.scene || '');
			console.log('开始获取 scene:', scene);
			if (scene) {
				this.$store.commit('setErweima', scene);
				uni.setStorageSync('erweima', scene);
				console.log('已存储 scene:', scene);
			} else {
				console.log('未获取到 scene 参数');
			}
			// Check if large-promo-overlay was previously closed
			const isLargePromoClosed = uni.getStorageSync('largePromoClosed');
			if (isLargePromoClosed) {
				this.showLargePromoPopup = false;
			}
			await this.initData();
			await this.getBottom();
		},
		async onPullDownRefresh() {
			console.log('开始下拉刷新');
			if (this.isRefreshing) {
				uni.stopPullDownRefresh();
				return;
			}
			this.isRefreshing = true;
			try {
				await this.initData();
				await this.getBottom();
			} catch (error) {
				console.error('刷新失败:', error);
			} finally {
				this.isRefreshing = false;
				uni.stopPullDownRefresh();
			}
		},
		async onShow() {
			const autograph = uni.getStorageSync("autograph");
			if (autograph && autograph !== "") {
				await this.getSearchList();
			}
		},
		onShareAppMessage() {},
		onPageScroll(e) {
			if (!this.isPopupShrunk) {
				this.isPopupShrunk = true;
			}
			if (this.scrollTimeout) {
				clearTimeout(this.scrollTimeout);
			}
			this.scrollTimeout = setTimeout(() => {
				this.isPopupShrunk = false;
			}, 3000);
		},
		methods: {
				
			async tetxlogin(){
				try {
					const params = {
						"phone": "17856179093",
						"password": "a03c37bfe64c92bb510b41142878e6a1",
						"platform": 1,
						"registrationId": ""
					};

					const response = await this.$api.base.appLoginByPass(params);
					console.log('登录响应:', response);

					// 获取响应头并提取token
					const headers = response.header;
					console.log('Response Headers:', headers);

					const token = extractTokenFromHeaders(headers);
					if (token) {
						console.log('提取到的Token:', token);
						// 在这里使用提取到的Token
					} else {
						console.log('未找到autograph相关的token');
					}
				} catch (error) {
					console.error('请求失败:', error);
				}
			},
			
			
			
			closeLargePromoPopup() {
				this.showLargePromoPopup = false;
				// Save the closed state to local storage
				uni.setStorageSync('largePromoClosed', true);
			},
			grabDeal() {
				this.showConfirm();
			},
			closeActivityPopup() {
				this.showActivityPopup = false;
			},
			showConfirm() {
				this.showConfirmPopup = true;
			},
			closeConfirmPopup() {
				this.showConfirmPopup = false;
			},
			closeLoginPopup() { // Added: Close login popup
				this.showLoginPopup = false;
			},
			goToLogin() { // Added: Navigate to login page
				this.showLoginPopup = false;
				this.showConfirmPopup = false;
				uni.navigateTo({
					url: '/pages/mine',
					fail: (err) => {
						console.error("导航到登录页面失败:", err);
						uni.showToast({
							title: "跳转失败",
							icon: "none",
						});
					},
				});
			},
			confirmToHuodongParity() {
				// Check if user is logged in by retrieving token
				const token = uni.getStorageSync('token');
				if (!token || token === '') {
					this.showLoginPopup = true;
					return;
				}

				// Existing logic if token exists
				this.showConfirmPopup = false;
				this.showLargePromoPopup = false;
				// Save the closed state to local storage when confirming
				uni.setStorageSync('largePromoClosed', true);
				if (this.isPageLoaded) {
					this.goUrl('/user/huodong_parity?id=519&type=1');
				} else {
					uni.showToast({
						icon: 'none',
						title: this.showmsg,
						duration: 1000
					});
				}
			},
			...mapActions(["serviceCate"]),
			...mapMutations(["updatePosition"]),
			getlingqu(item) {
				if (item.haveGet == 1) {
					uni.showToast({
						icon: 'none',
						title: '已领取过了',
						duration: 1000
					});
					return;
				}
				const obj = {
					couponId: [item.id]
				};
				this.$api.service.getWelfare(obj).then(res => {
					console.log(res)
					if (res.code === "200") {
						uni.showToast({
							icon: 'success',
							title: '领取成功',
							duration: 1000
						});
						this.couponList = this.couponList.map(coupon => {
							if (coupon.id === item.id) {
								return {
									...coupon,
									haveGet: 1
								};
							}
							return coupon;
						});
						this.getCoupon();
					} else {
						uni.showToast({
							icon: 'error',
							title: '领取失败',
							duration: 1000
						});
					}
				}).catch(err => {
					console.error("领取优惠券失败:", err);
					uni.showToast({
						icon: 'error',
						title: err,
						duration: 1000
					});
				});
			},
			debouncedGetlingqu: debounce(function(item) {
				this.getlingqu(item);
			}, 1000),
			getBottom() {
				this.isServiceLoading = true;
				return this.$api.service.getBottom(this.servicecity).then(res => {
					if (res && Array.isArray(res.data)) {
						this.service = res.data;
					} else {
						console.warn("getBottom 返回数据无效或为空:", res);
						this.service = [];
						uni.showToast({
							title: "服务数据加载失败",
							icon: "none",
						});
					}
				}).catch(err => {
					console.error("获取底部数据失败:", err);
					this.service = [];
					uni.showToast({
						title: "服务数据加载失败",
						icon: "none",
					});
				}).finally(() => {
					this.isServiceLoading = false;
				});
			},
			async initData() {
				if (this.isLoading) return;
				this.isLoading = true;
				try {
					await Promise.all([
						this.copyMethod(),
						// this.gethuodongconfig(),
						this.getNowPosition(),
						// this.getCoupon(),
					]);
				} catch (err) {
					console.error("初始化数据失败:", err);
					uni.showToast({
						title: "数据加载失败",
						icon: "none",
					});
				} finally {
					this.isLoading = false;
				}
			},
			getcount() {
				this.$api.service.huodongcount().then(res => {
					if (res.code === "-1") {
						this.isPageLoaded = false;
						this.showmsg = res.msg
					} else {
						this.isPageLoaded = true;
					}
					console.log(res)
				})
			},
			gethuodongconfig() {
				this.$api.service.huodongselectActivityConfig().then(res => {
					if (res.code === "200") {
						this.getcount()
						this.huodongdata = res.data
						this.isPageLoaded = true;
						console.log(this.huodongdata)
					}
					console.log(res)
				})
			},
			godetils(id) {
				if (!id) {
					uni.showToast({
						title: "无效的商品 ID",
						icon: "none",
					});
					return;
				}
				uni.navigateTo({
					url: `/pages/details/details?id=${id}`,
					fail: (err) => {
						console.error("跳转失败:", err);
						uni.showToast({
							title: "跳转失败: " + err.errMsg,
							icon: "none",
						});
					},
				});
			},
			async getSearchList() {
				try {
					const res = await this.$api.service.getHotSearch();
					this.SearchList = res.length > 3 ? res.slice(0, 3) : res;
				} catch (err) {
					console.error("获取热搜失败:", err);
				}
			},
			async getCoupon() {
				try {
					let userId = uni.getStorageSync('userId');
					const res = await this.$api.service.getWelfareList({
						userId: userId
					});
					this.couponList = res.list.slice(0, 3);
				} catch (err) {
					console.error("获取优惠券失败:", err);
				}
			},
			goUrl(e) {
				uni.navigateTo({
					url: e,
					fail: (err) => {
						console.error("导航失败:", err);
					},
				});
			},
			async copyMethod() {
				try {
					const res4 = await this.$api.service.getIndex();
					// const res5 = await this.$api.service.getServiceCate();
					if (res4.code === "200") {
						this.bannerList = res4.data.banner;
						this.list1 = res4.data.banner.map((item) => item.img);
						this.baseList = res4.data.jingang;
						this.text1 = res4.data.notices.map((item) => item.content);
						this.service = res4.data.serviceCate;
					}
				} catch (err) {
					console.error("获取首页数据失败:", err);
				}
			},
			getNowPosition() {
				return new Promise((resolve) => {
					uni.getLocation({
						type: "gcj02",
						isHighAccuracy: true,
						accuracy: "best",
						success: (res) => {
							uni.setStorageSync("lat", res.latitude);
							uni.setStorageSync("lng", res.longitude);
							uni.request({
								url: `https://restapi.amap.com/v3/geocode/regeo?key=4272f5716dfd17882409f306c0299666&location=${res.longitude},${res.latitude}`,
								success: (res1) => {
									console.log(res1)
									this.district = res1.data.regeocode.addressComponent
										.district
									console.log(this.district)
									this.servicecity=res1.data.regeocode.addressComponent.city
									const province = res1.data.regeocode.addressComponent
										.province;
									this.position = typeof res1.data.regeocode
										.addressComponent.city === "string" ?
										res1.data.regeocode.addressComponent.city :
										province;
									uni.setStorageSync("city", {
										city_id: res1.data.regeocode
											.addressComponent.adcode,
										position: this.position,
									});
									this.updatePosition({
										key: "position",
										val: this.position,
									});
									resolve();
								},
								fail: (err) => {
									console.error("逆地理编码失败:", err);
									resolve();
								},
							});
						},
						fail: (err) => {
							console.error("获取定位失败:", err);
							resolve();
						},
					});
				});
			},
		},
		computed: {
			...mapState({
				primaryColor: (state) => state.config.configInfo.primaryWidth,
				subColor: (state) => state.config.configInfo.subColor,
				configInfo: (state) => state.config.configInfo,
				commonOptions: (state) => state.user.commonOptions,
				userInfo: (state) => state.user.userInfo,
				userPageType: (state) => state.user.userPageType,
				mineInfo: (state) => state.user.mineInfo,
				position: (state) => state.position,
				serviceCateData: (state) => state.serviceCateData,
			}),
		},
	};
</script>

<style scoped lang="scss">
	/* Added: Styles for confirm popup */
	.confirm-popup-overlay {
		position: fixed;
		top: 0;
		left: 0;
		width: 100vw;
		height: 100vh;
		background-color: rgba(0, 0, 0, 0.7);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 1002;
		transition: opacity 0.3s ease-in-out;
	}

	.confirm-popup-container {
		width: 600rpx;
		background: #fff;
		border-radius: 20rpx;
		padding: 40rpx;
		box-sizing: border-box;
	}

	.confirm-popup-content {
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.confirm-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 20rpx;
	}

	.confirm-text {
		font-size: 28rpx;
		color: #666;
		line-height: 40rpx;
		text-align: left;
		width: 100%;
		margin-bottom: 40rpx;
	}

	.confirm-buttons {
		display: flex;
		justify-content: space-between;
		width: 100%;
	}

	.confirm-btn {
		width: 200rpx;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		border-radius: 40rpx;
		font-size: 28rpx;
	}

	.confirm-btn.cancel {
		background: #f2f3f4;
		color: #666;
	}

	.confirm-btn.confirm {
		background: linear-gradient(270deg, #EA5533 0%, #EE8751 100%);
		color: #fff;
	}

	/* Existing styles */
	.large-promo-overlay,
	.activity-popup {
		transition: opacity 0.3s ease-in-out;
	}

	.large-promo-overlay[hidden],
	.activity-popup[hidden] {
		opacity: 0;
		pointer-events: none;
	}

	.content {
		transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
	}

	.content[hidden] {
		opacity: 0;
		transform: translateY(20rpx);
	}

	@keyframes hand-press {
		0% {
			transform: scale(1);
		}

		50% {
			transform: scale(0.9);
		}

		100% {
			transform: scale(1);
		}
	}

	.pulse-text {
		animation: pulse-animation 0.5s infinite ease-in-out;
	}

	.large-promo-overlay {
		position: fixed;
		top: 0;
		left: 0;
		width: 100vw;
		height: 100vh;
		background-color: rgba(0, 0, 0, 0.7);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 1001;
	}

	.large-promo-container {
		position: relative;
		width: 600rpx;
		height: 480rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.promo-background-area {
		position: absolute;
		top: 0;
		width: 100%;
		height: 252rpx;
		background: radial-gradient(circle at 50% 120%, #ff8964, #dd4a34);
		border-radius: 14rpx;
	}

	.promo-ac {
		position: absolute;
		top: -100rpx;
		left: 50%;
		transform: translateX(-50%);
		width: 450rpx;
		z-index: 1;
	}

	.promo-character {
		position: absolute;
		bottom: -6rpx;
		right: 18rpx;
		width: 132rpx;
		z-index: 3;
	}

	.promo-coin {
		position: absolute;
		bottom: 72rpx;
		left: 24rpx;
		width: 54rpx;
		z-index: 3;
	}

	.promo-foreground-area {
		position: absolute;
		top: 200rpx;
		width: 348rpx;
		height: 180rpx;
		border-radius: 24rpx;
		z-index: 2;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding-top: 24rpx;
		box-sizing: border-box;
	}

	.promo-price {
		color: #f82c28;
		font-weight: 900;
		display: flex;
		align-items: baseline;
		line-height: 1;
		text-shadow: -1px -1px 0 #fff, 1px -1px 0 #fff, -1px 1px 0 #fff, 1px 1px 0 #fff;
	}

	.price-val {
		font-size: 96rpx;
	}

	.price-unit {
		font-size: 30rpx;
		margin-left: 6rpx;
	}

	.promo-subtitle {
		margin-top: 12rpx;
		background-color: #fadda6;
		color: #f82c28;
		font-size: 22rpx;
		font-weight: bold;
		padding: 5rpx 18rpx;
		border-radius: 18rpx;
	}

	.promo-button-area {
		position: absolute;
		bottom: -10rpx;
		z-index: 5;
		width: 450rpx;
		height: 60rpx;
		border-radius: 30rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		color: #fff;
		font-size: 26rpx;
		font-weight: bold;
		text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
		cursor: pointer;
		animation: pulse-animation 0.5s infinite ease-in-out;
	}

	.button-image {
		width: 320rpx !important;
		height: 100%;
	}

	.hand-pointer-animation {
		position: absolute;
		right: 30rpx;
		bottom: -12rpx;
		animation: hand-press 1.5s infinite;
	}

	.hand-pointer-img {
		width: 48rpx;
		height: 48rpx;
	}

	.promo-close-btn {
		position: absolute;
		bottom: -80rpx;
		left: 50%;
		transform: translateX(-50%);
		width: 42rpx;
		height: 42rpx;
		border: 2rpx solid rgba(255, 255, 255, 0.7);
		border-radius: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 4;
	}

	@keyframes pulse-animation {
		0% {
			transform: scale(1);
		}

		50% {
			transform: scale(1.05);
		}

		100% {
			transform: scale(1);
		}
	}

	.activity-popup {
		position: fixed;
		right: 16rpx;
		bottom: 600rpx;
		z-index: 999;
		width: 120rpx;
		background: linear-gradient(180deg, #FFE1C1 0%, #FF7B5A 100%);
		border-radius: 12rpx;
		border: 2rpx solid #FFF;
		box-shadow: 0rpx 4rpx 8rpx rgba(0, 0, 0, 0.2);
		padding: 6rpx;
		cursor: pointer;
		transition: transform 0.3s ease-in-out;
	}

	.activity-popup-shrunk {}

	.activity-popup-shrunk .popup-content {
		opacity: 1;
	}

	.popup-content {
		display: flex;
		flex-direction: column;
		align-items: center;
		text-align: center;
		transition: opacity 0.3s ease-in-out;
	}

	.popup-header-image {
		width: 70rpx;
		height: 40rpx;
		margin-top: 4rpx;
	}

	.popup-text-main {
		font-size: 20rpx;
		color: #A34A00;
		font-weight: bold;
		margin-top: 6rpx;
	}

	.popup-text-price {
		font-size: 18rpx;
		color: #E53935;
		margin-top: 2rpx;
		font-weight: 500;
		white-space: nowrap;
	}

	.price-number {
		font-size: 24rpx;
		font-weight: bold;
	}

	.popup-action-btn {
		width: 90rpx;
		margin-top: 8rpx;
		margin-bottom: 4rpx;
		padding: 6rpx 0;
		background: linear-gradient(270deg, #FF5A36 0%, #F60100 100%);
		border-radius: 18rpx;
		color: #FFFFFF;
		font-size: 18rpx;
		font-weight: bold;
		text-align: center;
		animation: pulse-animation 0.5s infinite ease-in-out;
	}

	.close-btn {
		position: absolute;
		top: -14rpx;
		right: -14rpx;
		width: 28rpx;
		height: 28rpx;
		background-color: #888888;
		color: #fff;
		border-radius: 50%;
		border: 2rpx solid #fff;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 20rpx;
		line-height: 28rpx;
		z-index: 1000;
	}

	.page {
		background-color: #599EFF;
		min-height: 100vh;
		padding-bottom: 80rpx;
	}

	.content {
		width: 100%;
		height: 100%;
		background-color: #fff;
		border-radius: 40rpx 40rpx 0 0;
		padding: 0 30rpx;
		padding-top: 18rpx;
		padding-bottom: 50rpx;
		overflow: auto;
	}

	.search_position {
		width: 630rpx;
		height: 72rpx;
		background: #F1F1F1;
		border-radius: 36rpx;
		display: flex;
		align-items: center;
		padding: 0 15rpx;
		position: relative;
	}

	.position {
		font-size: 28rpx;
		color: #333333;
		margin: 0 12rpx;
	}

	.shu {
		margin: 0 15rpx;
		color: rgba(0, 0, 0, 0.2);
	}

	input {
		margin-left: 22rpx;
		font-size: 28rpx;
		color: #ADADAD;
		width: 260rpx;
	}

	.btn {
		width: 112rpx;
		height: 56rpx;
		background: #2E80FE;
		border-radius: 28rpx;
		font-size: 28rpx;
		color: #FFFFFF;
		line-height: 56rpx;
		text-align: center;
		position: absolute;
		right: 20rpx;
	}

	.img {
		margin-top: 20rpx;
	}

	.tag {
		display: flex;
		justify-content: space-around;
		color: #BDD4FD;
		margin-top: 10rpx;
	}

	.tag_item {
		display: flex;
		align-items: center;
	}

	.tag_item text {
		font-size: 24rpx;
		color: #333333;
		margin-left: 14rpx;
	}

	.grid {
		margin-top: 40rpx;
	}

	.grid-container {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
	}

	.grid-item {
		width: 20%;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 10rpx 0;
	}

	.swiper {
		width: 690rpx;
		height: 90rpx;
		margin-top: 40rpx;
		display: flex;
		align-items: center;
		padding: 0 32rpx;
		border-radius: 16rpx;
		border: 2rpx solid #F7F6F6;
	}

	.swiper span {
		font-size: 32rpx;
		font-weight: 600;
		color: #000;
	}

	.swiper span text {
		color: #E72427;
	}

	.swiper .shu {
		font-weight: 200;
		color: #E6E6E6;
		margin-left: 20rpx;
	}

	.welfare {
		margin-top: 38rpx;
		width: 690rpx;
		height: 325rpx;
		background-image: url("../static/images/9243.png");
		background-size: cover;
		padding: 22rpx;
		position: relative;
	}

	.welfare .top {
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: 100%;
	}

	.welfare .top .left {
		display: flex;
		align-items: center;
		flex: 1;
	}

	.welfare .top .left text {
		margin-left: 16rpx;
		font-size: 32rpx;
		font-weight: 600;
		color: #451815;
	}

	.welfare .top .btn {
		width: 124rpx;
		height: 46rpx;
		background: linear-gradient(270deg, #EA5533 0%, #EE8751 100%);
		border-radius: 24rpx;
		font-size: 20rpx;
		color: #FFFFFF;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 14rpx;
		flex-shrink: 0;
	}

	.welfare .bottom {
		width: 646rpx;
		margin-top: 12rpx;
		display: flex;
		border-radius: 12rpx;
		background-color: #fff;
	}

	.welfare .bottom .right {
		flex: 1;
		display: flex;
		justify-content: space-around;
		align-items: center;
	}

	.welfare .bottom .right .right_item {
		width: 136rpx;
		height: 198rpx;
		background: linear-gradient(180deg, #FEF7EC 0%, #FCEFDF 100%);
		border-radius: 12rpx;
		padding-top: 28rpx;
	}

	.welfare .bottom .right .right_item .box1 {
		font-size: 44rpx;
		font-weight: 600;
		color: #E55138;
		text-align: center;
	}

	.welfare .bottom .right .right_item .box1 span {
		font-size: 24rpx;
	}

	.welfare .bottom .right .right_item .box2 {
		margin-top: 12rpx;
		font-size: 24rpx;
		color: #E55138;
		text-align: center;
	}

	.welfare .bottom .right .right_item .box3 {
		margin: 12rpx auto 0;
		width: 98rpx;
		height: 36rpx;
		background: linear-gradient(270deg, #EA5533 0%, #EE8751 100%);
		border-radius: 18rpx;
		font-size: 16rpx;
		color: #FFFFFF;
		line-height: 36rpx;
		text-align: center;
	}

	.service {
		margin-top: 40rpx;
	}

	.service .head {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.service .head .left {
		font-size: 36rpx;
		font-weight: 500;
		color: #333333;
	}

	.service .head .right {
		font-size: 24rpx;
		color: #999999;
		display: flex;
		align-items: center;
	}

	.service .se_main {
		display: flex;
		justify-content: space-between;
		flex-wrap: wrap;
		margin-top: 20rpx;
	}

	.service .se_main .se_item {
		width: 336rpx;
		height: 428rpx;
		background: #FFFFFF;
		border-radius: 20rpx;
		border: 2rpx solid #F3F3F3;
		display: flex;
		flex-wrap: wrap;
		justify-content: center;
		padding-top: 20rpx;
		margin-bottom: 20rpx;
	}

	.service .se_main .se_item .lbox {
		margin-top: 10rpx;
	}

	.service .se_main .se_item .lbox .name {
		font-size: 32rpx;
		font-weight: 500;
		color: #333333;
		text-align: center;
	}

	.service .se_main .se_item .lbox .baojia {
		margin: 6rpx auto 0;
		width: 140rpx;
		height: 50rpx;
		background: #FFFFFF;
		border-radius: 8rpx;
		border: 2rpx solid #2E80FE;
		font-size: 24rpx;
		color: #2E80FE;
		line-height: 50rpx;
		text-align: center;
	}

	.tips {
		margin-top: 32rpx;
		font-size: 36rpx;
		font-weight: 500;
		color: #333333;
		text-align: center;
	}

	.tips2 {
		font-size: 24rpx;
		color: #333333;
		margin-top: 16rpx;
		text-align: center;
	}
</style>