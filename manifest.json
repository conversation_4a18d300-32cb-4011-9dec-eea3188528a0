{
    "name" : "今师傅",
    "sassImplementationName" : "node-sass",
    "appid" : "__UNI__460500C",
    // "appid" : "__UNI__AE4A250",
    "description" : "今师傅",
    "versionName" : "1.1.2",
    "versionCode" : 112,
    "transformPx" : false,
    "uniStatistics" : {
        "enable" : false //全局关闭  
    },
    "mp-weixin" : {
        "appid" : "wx49c2324ccd5681b0",
        "setting" : {
            "urlCheck" : false,
            "postcss" : true,
            "minified" : true,
            "es6" : true
        },
        "usingComponents" : true,
        "optimization" : {
            "subPackages" : true
        },
        "plugins" : {},
        "permission" : {
            "scope.userLocation" : {
                "desc" : "您的位置将用于确认师傅与您的距离，以便更好的为您服务"
            }
        },
        "requiredPrivateInfos" : [ "getLocation", "chooseLocation" ],
        "navigateToMiniProgramAppIdList" : [],
        "preloadRule" : {},
        "uniStatistics" : {
            "enable" : true
        }
    },
    "h5" : {
        "title" : "",
        "domain" : "http://www.amtn.shop/",
        // "domain" : "https://massage.cncnconnect.com/",
        // "devServer" : {
        //     "https" : false
        // },
        "router" : {
            "base" : "/h5/"
        },
        "devServer" : {
            "disableHostCheck" : true,
            "proxy" : {
                "/api" : {
                    "target" : "http://www.amtn.shop",
                    // "target" : "https://massage.cncnconnect.com",
                    "changeOrigin" : true,
                    "secure" : false,
                    "pathRewrite" : {
                        "^/api" : ""
                    }
                }
            }
        },
        "sdkConfigs" : {
            "maps" : {
                "qqmap" : {
                    "key" : "MMRBZ-WVW3I-GWYGP-UJPON-SESGT-MXFJZ"
                }
            }
        }
    },
    "app-plus" : {
        /* 5+App特有相关 */
        "compatible" : {
            "ignoreVersion" : true //true表示忽略版本检查提示框，HBuilderX1.9.0及以上版本支持  
        },
        "usingComponents" : true,
        "splashscreen" : {
            "alwaysShowBeforeRender" : false,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        "modules" : {
            "OAuth" : {},
            "Geolocation" : {},
            "Maps" : {},
            "Payment" : {},
            "Share" : {},
            "VideoPlayer" : {},
            "Camera" : {}
        },
        /* 模块配置 */
        "distribute" : {
            /* 应用发布信息 */
            "permissionExternalStorage" : {
                "request" : "always",
                "prompt" : "应用保存运行状态等信息，需要获取读写手机存储（系统提示为访问设备上的照片、媒体内容和文件）权限，请允许。"
            },
            /* android打包配置 */
            "permissions" : [
                "<uses-feature android:name=\"android.hardware.camera\"/>",
                "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                "<uses-permission android:name=\"android.permission.ACCESS_SURFACE_FLINGER\"/>",
                "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                "<uses-permission android:name=\"android.permission.ACCESS_LOCATION_EXTRA_COMMANDS\"/>",
                "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
                "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                "<uses-permission android:name=\"android.permission.INTERNET\"/>",
                "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
                "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                "<uses-permission android:name=\"android.permission.READ_EXTERNAL_STORAGE\"/>",
                "<uses-permission android:name=\"android.permission.READ_CONTACTS\"/>",
                "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
                "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>",
                "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>",
                "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
            ],
            "sdkConfigs" : {
                "oauth" : {
                    "weixin" : {
                        "appid" : "wx5e2f07d307654aed",
                        "appsecret" : "d36e56321056e5ac5b7980854ba854ae",
                        "UniversalLinks" : "https://zskj.asia/ulink/"
                    },
                    "apple" : {}
                },
                "geolocation" : {
                    "amap" : {
                        "__platform__" : [ "ios", "android" ],
                        "appkey_ios" : "2036e9b214b103fcb49c00a23de129e3",
                        "appkey_android" : "2036e9b214b103fcb49c00a23de129e3",
                        "name" : "amapAWhSyGmpD"
                    }
                },
                "maps" : {
                    "amap" : {
                        "appkey_ios" : "2036e9b214b103fcb49c00a23de129e3",
                        "appkey_android" : "2036e9b214b103fcb49c00a23de129e3"
                    }
                },
                "payment" : {
                    "weixin" : {
                        "__platform__" : [ "ios", "android" ],
                        "appid" : "wx5e2f07d307654aed",
                        "UniversalLinks" : "https://zskj.asia/ulink/"
                    }
                },
                "share" : {
                    "weixin" : {
                        "appid" : "wx5e2f07d307654aed",
                        "UniversalLinks" : "https://zskj.asia/ulink/"
                    }
                },
                "ad" : {}
            },
            "android" : {
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_LOCATION_EXTRA_COMMANDS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_SURFACE_FLINGER\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CLEAR_APP_USER_DATA\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.INTERNET\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ],
                "abiFilters" : [ "armeabi-v7a", "arm64-v8a", "x86" ]
            },
            "icons" : {
                "android" : {
                    "hdpi" : "C:/Users/<USER>/Desktop/APP/iloveimg-converted (1)/72.png",
                    "xhdpi" : "C:/Users/<USER>/Desktop/APP/iloveimg-converted (1)/96.png",
                    "xxhdpi" : "C:/Users/<USER>/Desktop/APP/iloveimg-converted (1)/144.png",
                    "xxxhdpi" : "C:/Users/<USER>/Desktop/APP/iloveimg-converted (1)/192.png"
                },
                "ios" : {
                    "appstore" : "C:/Users/<USER>/Desktop/APP/iloveimg-converted (1)/1024.png",
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png"
                    },
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png"
                    }
                }
            },
            "ios" : {
                "deploymentTarget" : "13.0",
                "dSYMs" : false,
                "capabilities" : {
                    "entitlements" : {
                        "com.apple.developer.associated-domains" : [ "applinks:https://zskj.asia" ]
                    }
                },
                "idfa" : false,
                "privacyDescription" : {
                    "NSPhotoLibraryUsageDescription" : "用户申请服务时从相册中选择图片用以更好的让服务方了解情况",
                    "NSPhotoLibraryAddUsageDescription" : "保存用户所需要的图片",
                    "NSCameraUsageDescription" : "用户申请服务时使用摄像头中拍摄照片用以更好的让服务方了解情况",
                    "NSLocationAlwaysAndWhenInUseUsageDescription" : "获取用户地理位置用以服务方获取服务距离以及提供服务",
                    "NSLocationWhenInUseUsageDescription" : "获取用户地理位置用以服务方获取服务距离以及提供服务",
                    "NSLocationAlwaysUsageDescription" : "获取用户地理位置用以服务方获取服务距离以及提供服务"
                }
            }
        },
        "ios" : {},
        /* ios打包配置 */
        "sdkConfigs" : {
            "payment" : {
                "weixin" : {
                    "__platform__" : [ "ios", "android" ],
                    "appid" : "wx49c2324ccd5681b0",
                    "UniversalLinks" : "https://zskj.asia/ulink/"
                }
            },
            "oauth" : {
                "weixin" : {
                    "appid" : "wx49c2324ccd5681b0",
                    "appsecret" : "cdc37c403a2d828fd8effcf384a42c92",
                    "UniversalLinks" : "https://zskj.asia/ulink/"
                }
            },
            "push" : {},
            "share" : {
                "weixin" : {
                    "appid" : "wx49c2324ccd5681b0",
                    "UniversalLinks" : "https://zskj.asia/ulink/"
                }
            },
            "ad" : {},
            "geolocation" : {
                "amap" : {
                    "__platform__" : [ "android" ],
                    "appkey_ios" : "",
                    "appkey_android" : "6cbbd69cc7973aba0d1c834accb2ceb3"
                }
            },
            "maps" : {
                "amap" : {
                    "appkey_ios" : "6cbbd69cc7973aba0d1c834accb2ceb3",
                    "appkey_android" : "6cbbd69cc7973aba0d1c834accb2ceb3"
                }
            }
        }
    },
    "nativePlugins" : {},
    "_spaceID" : "8a1db412-049f-4be6-894a-755ffe5ebe5b",
    "mp-alipay" : {
        "appid" : "9021000149674982"
    }
}
