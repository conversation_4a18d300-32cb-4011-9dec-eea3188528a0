<template>
  <view class="container">
    <view class="title">Cookie 解析测试</view>
    
    <view class="section">
      <view class="section-title">测试 Cookie 解析器</view>
      <button @click="runCookieTests" class="test-btn">运行测试</button>
    </view>
    
    <view class="section">
      <view class="section-title">测试登录接口</view>
      <button @click="testLoginAPI" class="test-btn">测试登录</button>
    </view>
    
    <view class="section">
      <view class="section-title">测试结果</view>
      <view class="result-area">
        <text v-for="(log, index) in logs" :key="index" class="log-item">{{ log }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import { testCookieParser, extractTokenFromHeaders, extractAutographToken } from '@/utils/cookieParser.js';

export default {
  data() {
    return {
      logs: []
    };
  },
  methods: {
    addLog(message) {
      this.logs.push(`[${new Date().toLocaleTimeString()}] ${message}`);
      console.log(message);
    },
    
    runCookieTests() {
      this.logs = [];
      this.addLog('开始运行 Cookie 解析测试...');
      
      // 运行内置测试
      testCookieParser();
      
      // 手动测试您提到的具体情况
      this.addLog('测试您的具体情况...');
      
      const yourCookie = 'autograph-test=Gj0agTgmSFlMn5E2tozcyPwfENpySPtFHKwbD4R4FO8Qz4fujQeU3fvGOsd84Dis; Max-Age=25920; Expires=Wed, 30 Jul 2025 23:16:40 +0800; Path=/';
      const extractedToken = extractAutographToken(yourCookie);
      
      this.addLog(`输入Cookie: ${yourCookie}`);
      this.addLog(`提取结果: ${extractedToken}`);
      this.addLog(`期望结果: Gj0agTgmSFlMn5E2tozcyPwfENpySPtFHKwbD4R4FO8Qz4fujQeU3fvGOsd84Dis`);
      this.addLog(`测试通过: ${extractedToken === 'Gj0agTgmSFlMn5E2tozcyPwfENpySPtFHKwbD4R4FO8Qz4fujQeU3fvGOsd84Dis'}`);
      
      // 测试响应头解析
      this.addLog('测试响应头解析...');
      const testHeaders = {
        'Content-Type': 'application/json',
        'Set-Cookie': yourCookie,
        'Date': 'Wed, 30 Jul 2025 08:04:40 GMT'
      };
      
      const headerToken = extractTokenFromHeaders(testHeaders);
      this.addLog(`从响应头提取: ${headerToken}`);
      this.addLog(`响应头测试通过: ${headerToken === 'Gj0agTgmSFlMn5E2tozcyPwfENpySPtFHKwbD4R4FO8Qz4fujQeU3fvGOsd84Dis'}`);
      
      this.addLog('Cookie 解析测试完成！');
    },
    
    async testLoginAPI() {
      this.addLog('开始测试登录接口...');
      
      try {
        const params = {
          phone: '17856179093',
          code: '123456', // 这里需要真实的验证码
          platform: 2,
          registrationId: ''
        };
        
        this.addLog('调用 appLoginByCode 接口...');
        const response = await this.$api.base.appLoginByCode(params);
        
        this.addLog('登录响应接收成功');
        this.addLog(`响应代码: ${response.code}`);
        this.addLog(`响应消息: ${response.msg}`);
        
        if (response.header) {
          this.addLog('开始解析响应头...');
          const token = extractTokenFromHeaders(response.header);
          
          if (token) {
            this.addLog(`成功提取token: ${token.substring(0, 20)}...`);
          } else {
            this.addLog('未能提取到token');
            this.addLog(`响应头内容: ${JSON.stringify(response.header)}`);
          }
        } else {
          this.addLog('响应中没有header信息');
        }
        
      } catch (error) {
        this.addLog(`登录测试失败: ${error.message}`);
        console.error('登录测试错误:', error);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.container {
  padding: 40rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 40rpx;
  color: #333;
}

.section {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.test-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #fff;
  border: none;
  border-radius: 12rpx;
  font-size: 30rpx;
  font-weight: bold;
}

.result-area {
  max-height: 600rpx;
  overflow-y: auto;
  background: #f8f9fa;
  border-radius: 8rpx;
  padding: 20rpx;
}

.log-item {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 10rpx;
  word-break: break-all;
}
</style>
