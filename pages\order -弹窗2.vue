<template>
	<view class="page">
		<tabbar :cur="2"></tabbar>
		<u-modal :show="show" title="删除商品" content='确认要删除该订单吗' showCancelButton @confirm="confirmDel"
			@cancel="show=false"></u-modal>
		
		<view v-if="configModalShow" class="modal-overlay" @click="closeConfigModal">
			<view class="modal-container" @click.stop="">
				<view class="modal-header">
					<text class="modal-title">修改信息</text>
					<text class="close-btn" @click="closeConfigModal">×</text>
				</view>

				<view v-if="modalData.loading" class="loading-container">
					<text>加载中...</text>
				</view>

				<scroll-view v-else scroll-y="true" class="modal-scroll">
					<view class="config-content">
						<view class="card">
							<view class="bottom">
								<view class="left">已选：</view>
								<view class="right">
									<view class="">
										{{modalData.yikoujiaprice}}
									</view>
									<view class="tag" v-for="(item,index) in modalData.chooseArr" :key="index">{{item.name}}</view>
									<view class="tag" v-for="(item,index) in modalData.chosenInputValues" :key="'input-' + index">
										{{ item.problemDesc }}: {{ item.val }}
									</view>
								</view>
							</view>
						</view>
						
						<view class="chol" v-for="(item,index) in modalData.list" :key="index">
							<view class="choose">
								<view class="title"><span v-if="item.isRequired == 1">*</span>{{item.problemDesc}}</view>
								<view class="desc">{{item.problemContent}}</view>
								<view class="cho_box">
									<view class="box_item" v-for="(newItem,newIndex) in item.options" :key="newIndex"
										:style="newItem.choose?'border:2rpx solid #2E80FE;color: #2E80FE;':''"
										@click="chooseOne(index,newIndex,item.inputType)">
										{{newItem.name}}
										<view class="ok" :style="newItem.choose? '' : 'display:none;'">
											<uni-icons type="checkmarkempty" size="8" color="#fff"></uni-icons>
										</view>
									</view>
								</view>
							</view>
							<view class="fg"></view>
						</view>
						
						<view class="chol" v-for="item in modalData.list2" :key="item.id">
							<view class="choose">
								<view class="title"><span v-if="item.isRequired == 1">*</span>{{item.problemDesc}}</view>
								<view class="desc">{{item.problemContent}}</view>
								<view class="input-container">
									<input  
										type="text" 
										v-model="modalData.form.data[getFormIndex(item.id)].val" 
										:placeholder="'请输入' + item.problemDesc"
										class="form-input"
									/>
								</view>
							</view>
							<view class="fg"></view>
						</view>
						
						<view class="chol" v-for="(item,index) in modalData.list3" :key="index">
							<view class="choose">
								<view class="title"><span v-if="item.isRequired == 1">*</span>{{item.problemDesc}}</view>
								<view class="desc up">{{item.problemContent}}</view>
								<upload @upload="imgUpload" @del="imgUpload"
									:imagelist="modalData.form.data[getFormIndex(item.id)].val"
									:imgtype="getFormIndex(item.id)" text="上传图片" :imgsize="3">
								</upload>
							</view>
							<view class="fg"></view>
						</view>
					</view>
				</scroll-view>
				
				<view class="modal-footer">
					<view class="modal-btn cancel" @click="closeConfigModal">取消</view>
					<view class="modal-btn confirm" 
						:class="{ 'submitting': modalData.isSubmitting }" 
						@click="submitConfig">
						{{ modalData.isSubmitting ? '提交中...' : '保存' }}
					</view>
				</view>
			</view>
		</view>
		
		<view class="">
			<view class="car_item" v-for="(item,index) in list" :key="index">
				<view class="trash">
					<label class="checkbox">
						<radio class="small-radio" :checked="item.allChecked" @click="toggleAll(item)"
							color="#2979ff" />
						<text style="font-size:31rpx ; font-weight: 600;" class="name">{{item.name}}</text>
					</label>
					<u-icon name="trash" color="#2979ff" size="26" @click="goTrash(item)"></u-icon>
				</view>
				<view class="divider"></view>
				<view v-for="(item2,index2) in item.value" :key="index2" class="top">
					<label class="checkbox">
						<radio class="small-radio" :checked="item2.checked" @click="toggleItem(item, item2)"
							color="#2979ff" />
					</label>
					<image :src="item2.cover" mode=""></image>
					<view @click="showdetail(item2)" class="right">
						<div class="choose">
							已选：
							<span v-for="(item3, index3) in item2.settingVals" :key="index3"
								:class="{'last-item': index3 === item2.settingVals.length - 1}">
								{{ item3.val }}<template v-if="index3 !== item2.settingVals.length - 1">,</template>
							</span>
						</div>
						<view class="price">
							<text v-if="item2.price == 0">师傅报价</text>
							<text v-else>{{item2.price}}元/台起</text>
							<u-number-box v-model="item2.num" :min="1">
								<template slot="minus">
									<view @click="minus(item2)"
										style="width: 70rpx;height: 60rpx;background-color: #ebecee;display: flex;justify-content: center;align-items: center;">
										<u-icon name="minus" color="#333" size="16"></u-icon>
									</view>
								</template>
								<template slot="plus">
									<view @click="plus(item2)"
										style="width: 70rpx;height: 60rpx;background-color: #ebecee;display: flex;justify-content: center;align-items: center;">
										<u-icon name="plus" color="#333" size="16"></u-icon>
									</view>
								</template>
							</u-number-box>
						</view>
					</view>
				</view>
			</view>
		</view>

		<view class="footer">
			<view class="footer-left">
				<label class="checkbox">
					<radio class="large-radio" :checked="isAllChecked" @click="toggleAllCheck" color="#2979ff" />
					<text>全选</text>
				</label>
			</view>
			<view class="footer-center">
				<text>合计：</text>
				<text class="total-price">{{totalPrice}}元</text>
			</view>
			<view class="footer-right" @click="goToOrder">
				去下单
			</view>
		</view>
	</view>
</template>

<script>
import tabbar from "@/components/tabbar.vue"
export default {
	components: {
		tabbar
	},
	data() {
		return {
			show: false,
			value: 1,
			list: [],
			id: '',
			selectedIds: [],
			// 弹窗相关数据
			configModalShow: false,
			currentItem: null,
			modalData: {
				id: '',
				serviceId: '',
				chooseArr: [],
				chosenInputValues: [],
				list: [],
				list2: [],
				list3: [],
				serviceInfo: {},
				newsubbit: [],
				form: {
					data: [],
					carId: '',
					serviceId: '',
				},
				btArr: [],
				yikoujiaprice: '',
				isSubmitting: false
			}
		}
	},
	computed: {
		// 计算是否全选
		isAllChecked() {
			return this.list.length > 0 && this.list.every(item => item.allChecked);
		},
		// 计算总价
		totalPrice() {
			let total = 0;
			this.list.forEach(item => {
				item.value.forEach(subItem => {
					if (subItem.checked && subItem.price > 0) {
						total += subItem.price * subItem.num;
					}
				});
			});
			return total.toFixed(2);
		}
	},
	methods: {
		// 显示配置弹窗
		async showdetail(item) {
			console.log('显示配置弹窗，item:', item);
			this.currentItem = item;

			// Initialize modal data
			this.modalData = {
				id: item.id,
				serviceId: item.serviceId,
				chooseArr: [],
				chosenInputValues: [],
				list: [],
				list2: [],
				list3: [],
				serviceInfo: {},
				newsubbit: [],
				form: {
					data: [],
					carId: item.id,
					serviceId: item.serviceId,
				},
				btArr: [],
				yikoujiaprice: '',
				isSubmitting: false,
				loading: true
			};

			// First, show the modal
			this.configModalShow = true;

			// Then, load configuration data
			await this.loadConfigData();
		},

		// 加载配置数据
		async loadConfigData() {
			console.log('Start loading config data, modalData.id:', this.modalData.id, 'modalData.serviceId:', this.modalData.serviceId);
			try {
				// Get service info
				await this.$api.service.getserviceInfo(this.modalData.id).then(res => {
					console.log('Get service info response:', res);
					if(res.data && res.data.price && res.data.price !== 0) {
						this.modalData.yikoujiaprice = res.data.price;
					} else {
						this.modalData.yikoujiaprice = '';
					}
					this.modalData.serviceInfo = res.data || {};
				});

				// Get configuration info
				await this.$api.service.getcartsettingsInfo({
					id: this.modalData.serviceId
				}).then(ress => {
					console.log('Get config info response:', ress);
					let res = ress.data || [];
					console.log('Config info data:', res);

					res.forEach(item => {
						if (item.isRequired == 1) {
							this.modalData.btArr.push(item.id);
						}
						if (item.options) {
							item.options = JSON.parse(item.options);
							item.options = item.options.map(e => {
								return {
									serviceId: item.id,
									name: e,
									choose: false
								}
							});
						}
					});

					this.modalData.list = res.filter(item => item.inputType == 3 || item.inputType == 4);
					console.log('Radio/Checkbox data:', this.modalData.list);
					this.modalData.list.forEach((newItem) => {
						this.modalData.form.data.push({
							"serviceId": newItem.id,
							"settingId": this.modalData.id,
							"val": []
						});
					});

					this.modalData.list2 = res.filter(item => item.inputType == 1);
					console.log('Input field data:', this.modalData.list2);
					this.modalData.list2.forEach((newItem) => {
						this.modalData.form.data.push({
							"serviceId": newItem.id,
							"settingId": this.modalData.id,
							"val": ''
						});
					});

					this.modalData.list3 = res.filter(item => item.inputType == 2);
					console.log('Image upload data:', this.modalData.list3);
					this.modalData.list3.forEach((newItem) => {
						this.modalData.form.data.push({
							"serviceId": newItem.id,
							"settingId": this.modalData.id,
							"val": []
						});
					});
				});

				// Get cart info
				await this.$api.service.getcartinfo({
					ids: this.modalData.id
				}).then(ress => {
					console.log("Cart API Response:", ress);
					let cartData = [];
					if (ress.data && ress.data.length > 0 && ress.data[0].list) {
						cartData = ress.data[0].list;
					}
					console.log("Cart Info Data:", cartData);

					this.modalData.chosenInputValues = [];
					this.modalData.newsubbit = cartData;

					if (cartData && cartData.length > 0) {
						cartData.forEach(cartItem => {
							const formItemIndex = this.modalData.form.data.findIndex(f => f.serviceId === cartItem.settingId);

							if (formItemIndex !== -1) {
								const formItem = this.modalData.form.data[formItemIndex];
								const originalItem = this.modalData.list.find(l => l.id === cartItem.settingId) ||
													 this.modalData.list2.find(l => l.id === cartItem.settingId) ||
													 this.modalData.list3.find(l => l.id === cartItem.settingId);

								if (originalItem) {
									if (originalItem.inputType === 3 || originalItem.inputType === 4) {
										if (originalItem.options) {
											originalItem.options.forEach(option => {
												if (option.name === cartItem.val) {
													option.choose = true;
													if (!this.modalData.chooseArr.some(chosen => chosen.serviceId === option.serviceId && chosen.name === option.name)) {
														this.modalData.chooseArr.push(option);
													}
												}
											});
										}
										if (formItem.val && !Array.isArray(formItem.val)) {
											formItem.val = [formItem.val];
										}
										if (formItem.val && !formItem.val.includes(cartItem.val)) {
											formItem.val.push(cartItem.val);
										} else if (!formItem.val) {
											formItem.val = [cartItem.val];
										}
									} else if (originalItem.inputType === 1) {
										formItem.val = cartItem.val;
										this.modalData.chosenInputValues.push({
											problemDesc: originalItem.problemDesc,
											val: cartItem.val
										});
									} else if (originalItem.inputType === 2) {
										formItem.val = cartItem.val ? cartItem.val.split(',').filter(url => url) : [];
									}
								}
							}
						});
					}
					console.log("Final modalData:", this.modalData);
				});

				// Data loaded
				this.modalData.loading = false;
			} catch (error) {
				console.error('Failed to load config data:', error);
				this.modalData.loading = false;
				uni.showToast({
					icon: 'error',
					title: '加载失败',
					duration: 1000
				});
			}
		},

		// 关闭配置弹窗
		closeConfigModal() {
			this.configModalShow = false;
			this.currentItem = null;
		},

		// 获取表单索引
		getFormIndex(serviceId) {
			return this.modalData.form.data.findIndex(e => e.serviceId == serviceId);
		},

		// 选择选项
		chooseOne(i, j, inputType) {
			this.modalData.list[i].options[j].choose = !this.modalData.list[i].options[j].choose;
			if (inputType == 3) { // Single choice
				this.modalData.list[i].options.forEach((item, index) => {
					if (index == j) return;
					item.choose = false;
				});
				this.modalData.chooseArr = [];
				this.modalData.list.forEach(item => {
					item.options.forEach(tem => {
						if (tem.choose) {
							this.modalData.chooseArr.push(tem);
						}
					});
				});
			} else if (inputType == 4) { // Multiple choice
				this.modalData.chooseArr = [];
				this.modalData.list.forEach(item => {
					item.options.forEach(tem => {
						if (tem.choose) {
							this.modalData.chooseArr.push(tem);
						}
					});
				});
			}
		},

		// 图片上传
		imgUpload(e) {
			let { imagelist, imgtype } = e;
			let newFormData = [...this.modalData.form.data];
			newFormData[imgtype] = {
				...newFormData[imgtype],
				val: [...imagelist]
			};
			this.$set(this.modalData.form, 'data', newFormData);
		},

		// 提交配置
		submitConfig() {
			if (this.modalData.isSubmitting) {
				return;
			}

			this.modalData.isSubmitting = true;

			let copy_form = JSON.parse(JSON.stringify(this.modalData.form));
			let copynew = JSON.parse(JSON.stringify(this.modalData.newsubbit)).map(item => ({ id: item.id }));

			// Clear previous selections
			this.modalData.list.forEach(item => {
				const formIndex = copy_form.data.findIndex(e => e.serviceId == item.id);
				if (formIndex !== -1) {
					copy_form.data[formIndex].val = [];
				}
			});

			this.modalData.chooseArr.forEach(item => {
				const formIndex = copy_form.data.findIndex(e => e.serviceId == item.serviceId);
				if (formIndex !== -1) {
					if (!Array.isArray(copy_form.data[formIndex].val)) {
						copy_form.data[formIndex].val = [];
					}
					copy_form.data[formIndex].val.push(item.name);
				}
			});

			let open = true;
			copy_form.data.forEach(item => {
				let index = this.modalData.btArr.findIndex(e => e == item.serviceId);
				if (index != -1 && (item.val == '' || (Array.isArray(item.val) && item.val.length === 0))) {
					uni.showToast({
						icon: 'none',
						title: '请填写完整后提交',
						duration: 1500
					});
					open = false;
					return;
				}
				if (item.val === '' || (Array.isArray(item.val) && item.val.length === 0)) {
					item.val = "无";
				}
			});

			if (!open) {
				this.modalData.isSubmitting = false;
				return;
			}

			const data = copy_form.data.map((item, index) => ({
				id: copynew[index]?.id || item.serviceId,
				val: Array.isArray(item.val) ? item.val.join(',') : item.val
			}));

			const payload = {
				data,
				serviceId: this.modalData.form.serviceId,
				carId: copy_form.data[0]?.serviceId || this.modalData.form.serviceId
			};

			console.log('payload:', payload);

			this.$api.service.postorderinfo(payload).then(res => {
				if (res.code === "200") {
					uni.showToast({
						icon: 'success',
						title: '保存成功',
						duration: 1000
					});
					this.closeConfigModal();
					// Refresh list
					this.getList();
				} else {
					uni.showToast({
						icon: 'error',
						title: '请重新尝试',
						duration: 1000
					});
				}
			}).catch(err => {
				console.error('Submission failed:', err);
				uni.showToast({
					icon: 'error',
					title: 'Network error, please try again',
					duration: 1000
				});
			}).finally(() => {
				this.modalData.isSubmitting = false;
			});
		},

		// Original methods remain unchanged
		godetail() {
			uni.navigateTo({
				url:'../user/order_confirm'
			})
		},
		confirmDel() {
			this.show = false;
			console.log(this.selectedIds);
			this.$api.service.discar({
				ids: this.selectedIds
			}).then(() => {
				uni.showToast({
					icon: 'success',
					title: '删除成功'
				});
				setTimeout(() => {
					this.getList();
				}, 500);
			});
		},
		goTrash(item) {
			console.log(item);
			this.show = true;
			this.id = item.id;
			this.selectedIds = item.value.filter(subItem => subItem.checked).map(subItem => subItem.id);
			if (this.selectedIds.length === 0) {
				this.selectedIds = [item.id];
			}
		},
		plus(item) {
			this.$api.service.updatatocar({
				serviceId: item.serviceId,
				id: item.id,
				num: item.num+1
			}).then(() => {
				this.getList();
			});
		},
		minus(item) {
			if (item.num == 1) return;
			this.$api.service.updatatocar({
				serviceId: item.serviceId,
				id: item.id,
				num: item.num-1
			}).then(() => {
				this.getList();
			});
		},
		// Select/deselect all for a single category
		toggleAll(item) {
			item.allChecked = !item.allChecked;
			item.value.forEach(child => {
				child.checked = item.allChecked;
			});
		},
		// Select/deselect all items
		toggleAllCheck() {
			const newStatus = !this.isAllChecked;
			this.list.forEach(item => {
				item.allChecked = newStatus;
				item.value.forEach(child => {
					child.checked = newStatus;
				});
			});
		},
		// Single selection
		toggleItem(item, item2) {
			item2.checked = !item2.checked;
			// Check if all sub-items are selected
			item.allChecked = item.value.every(child => child.checked);
		},
		getList() {
			this.$api.service.seecar().then(res => {
				console.log(res);
				// Initialize selection status
				this.list = res.data.map(group => {
					return {
						...group,
						allChecked: false,
						value: group.value.map(item => {
							return {
								...item,
								checked: false
							};
						})
					};
				});
			});
		},
		goDown(item) {
			uni.redirectTo({
				url: `../user/commodity_details?id=${item.serviceId}`
			});
		},
		// Go to order
		goToOrder() {
			const selectedItems = [];
			this.list.forEach(item => {
				item.value.forEach(subItem => {
					if (subItem.checked) {
						selectedItems.push({
							id: subItem.serviceId,
							num: subItem.num
						});
					}
				});
			});

			if (selectedItems.length === 0) {
				uni.showToast({
					title: '请选择商品',
					icon: 'none'
				});
				return;
			}

			// Add order logic here
			console.log('Go to order', selectedItems);
			uni.showToast({
				title: '跳转下单页面',
				icon: 'none'
			});
		}
	},
	onLoad() {
		let token = uni.getStorageSync('token');
		console.log(111);
		if (!token || token == '') {
			return;
		} else {
			this.getList();
		}
	}
}
</script>

<style scoped lang="scss">
.page {
	background-color: #F8F8F8;
	height: 100vh;
	overflow: auto;
	padding: 40rpx 0;
	padding-bottom: 200rpx;

	.small-radio {
		transform: scale(0.8);
		transform-origin: center;
	}

	/* 调整最后一个 radio 为大尺寸 */
	.large-radio {
		transform: scale(1);
		transform-origin: center;
	}

	.car_item {
		margin: 0 auto;
		width: 686rpx;
		background: #FFFFFF;
		border-radius: 12rpx 12rpx 12rpx 12rpx;
		margin-bottom: 20rpx;
		padding: 0 20rpx;
		position: relative;

		.trash {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 20rpx 0;

			.checkbox {
				display: flex;
				align-items: center;

				.name {
					margin-left: 10rpx;
					font-size: 28rpx;
					color: #333;
				}
			}
		}

		.divider {
			height: 2rpx;
			background-color: #F2F3F6;
			margin: 0 -20rpx;
		}

		.top {
			padding: 36rpx 0;
			display: flex;
			align-items: center;
			border-bottom: 2rpx solid #F2F3F6;

			.checkbox {
				margin-right: 20rpx;
				display: flex;
				align-items: center;
			}

			image {
				width: 200rpx;
				height: 200rpx;
				margin-right: 20rpx;
			}

			.right {
				flex: 1;

				.name {
					font-size: 28rpx;
					font-weight: 500;
					color: #171717;
				}

				.choose {
					span {
						font-size: 24rpx;
						font-weight: 400;
						color: #ADADAD;
						margin-right: 4rpx;

						&.last-item {
							margin-right: 0;
						}
					}
				}

				.price {
					width: 100%;
					margin-top: 68rpx;
					font-size: 20rpx;
					font-weight: 500;
					color: #E72427;
					display: flex;
					justify-content: space-between;
					align-items: center;
				}
			}
		}
	}

	/* 底部结算栏样式 */
	.footer {
		position: fixed;
		bottom: 130rpx;
		left: 0;
		right: 0;
		height: 100rpx;
		background-color: #fff;
		display: flex;
		align-items: center;
		border-top: 1rpx solid #f2f2f2;
		padding: 0 60rpx;
		padding-right: 40rpx;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);

		.footer-left {
			flex: 1;
			display: flex;
			align-items: center;

			.checkbox {
				display: flex;
				align-items: center;

				text {
					margin-left: 10rpx;
					font-size: 28rpx;
					color: #333;
				}
			}
		}

		.footer-center {
			flex: 2;
			text-align: right;
			padding-right: 30rpx;

			text {
				font-size: 28rpx;
				color: #333;
			}

			.total-price {
				font-size: 32rpx;
				font-weight: bold;
				color: #E72427;
			}
		}

		.footer-right {
			width: 200rpx;
			height: 80rpx;
			background-color: #2979ff;
			color: #fff;
			border-radius: 40rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 32rpx;
		}
	}
}

/* 弹窗样式 */
.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: flex-end; /* Align to the bottom */
	justify-content: center;
	z-index: 9999;
}

.modal-container {
	width: 100%; /* Full width */
	max-height: 80vh; /* Max height 80% of viewport height */
	background: #fff;
	border-radius: 20rpx 20rpx 0 0; /* Rounded corners only at the top */
	display: flex;
	flex-direction: column;
	overflow: hidden;
	transform: translateY(100%); /* Start off-screen at the bottom */
	animation: slide-up 0.3s forwards ease-out; /* Animation for sliding up */
}

@keyframes slide-up {
	from {
		transform: translateY(100%);
	}
	to {
		transform: translateY(0);
	}
}

.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #f2f2f2;
	background: #fff;
	position: sticky; /* Keep header at the top */
	top: 0;
	z-index: 10;
}

.modal-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.close-btn {
	font-size: 40rpx;
	color: #999;
	line-height: 1;
	padding: 10rpx;
}

.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx;
	flex-grow: 1; /* Allow it to take available space */

	text {
		font-size: 28rpx;
		color: #666;
	}
}

.modal-scroll {
	flex: 1; /* Allow scroll-view to take up remaining height */
	overflow-y: auto;
}

.config-content {
	padding: 20rpx;
}

.card {
	width: 100%;
	background: #FFFFFF;
	box-shadow: 0rpx 0rpx 8rpx 2rpx rgba(0, 0, 0, 0.16);
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.card .bottom {
	display: flex;
}

.card .bottom .left {
	font-size: 24rpx;
	font-weight: 400;
	color: #999999;
	padding-top: 10rpx;
}

.card .bottom .right {
	flex: 1;
	margin-left: 20rpx;
	display: flex;
	flex-wrap: wrap;
	align-items: center;
}

.card .bottom .right .tag {
	width: fit-content;
	height: 44rpx;
	padding: 0 12rpx;
	background: #DCEAFF;
	border-radius: 4rpx;
	font-size: 16rpx;
	font-weight: 400;
	color: #2E80FE;
	line-height: 44rpx;
	text-align: center;
	margin: 10rpx;
}

.chol .choose {
	padding: 30rpx 0;
}

.chol .choose .title {
	font-size: 28rpx;
	font-weight: 500;
	color: #333333;
}

.chol .choose .title span {
	color: #E72427;
}

.chol .choose .input-container {
	margin-top: 30rpx;
	position: relative;
	width: 100%;
	min-height: 80rpx;
}

.chol .choose .form-input {
	box-sizing: border-box;
	width: 100%;
	height: 80rpx;
	background: #F7F7F7;
	border-radius: 12rpx;
	padding: 0 30rpx;
	font-size: 26rpx;
	line-height: 80rpx;
	border: 2rpx solid transparent;
	transition: all 0.2s ease;
}

.chol .choose .form-input:focus {
	background: #fff;
	border-color: #2E80FE;
	box-shadow: 0 0 0 4rpx rgba(46, 128, 254, 0.1);
	outline: none;
}

.chol .choose .desc {
	margin-top: 15rpx;
	font-size: 22rpx;
	font-weight: 400;
	color: #ADADAD;
}

.chol .choose .up {
	margin-bottom: 30rpx;
}

.chol .choose .cho_box {
	margin-top: 15rpx;
	display: flex;
	flex-wrap: wrap;
	padding-bottom: 15rpx; /* Add some padding to the bottom */
}

.chol .choose .cho_box .box_item {
	width: fit-content;
	padding: 0 15rpx;
	height: 50rpx;
	background: #FFFFFF;
	border-radius: 4rpx;
	border: 2rpx solid #D8D8D8;
	font-size: 22rpx;
	font-weight: 400;
	color: #ADADAD;
	line-height: 50rpx;
	margin-right: 15rpx;
	margin-bottom: 15rpx;
	position: relative;
}

.chol .choose .cho_box .box_item .ok {
	width: 18rpx;
	height: 18rpx;
	position: absolute;
	right: 0;
	bottom: 0;
	background-color: #2E80FE;
	display: flex;
	align-items: center;
	justify-content: center;
}

.chol .fg {
	width: 100%;
	height: 15rpx;
	background: #F3F4F5;
	margin: 15rpx 0;
}

.modal-footer {
	display: flex;
	padding: 30rpx;
	border-top: 1rpx solid #f2f2f2;
	gap: 20rpx;
	position: sticky; /* Keep footer at the bottom */
	bottom: 0;
	background: #fff;
	z-index: 10;
}

.modal-btn {
	flex: 1;
	height: 80rpx;
	border-radius: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
	font-weight: 400;

	&.cancel {
		background: #f5f5f5;
		color: #666;
	}

	&.confirm {
		background: #2e80fe;
		color: #ffffff;
		transition: all 0.2s ease;

		&.submitting {
			background: #8bb8ff;
			opacity: 0.7;
			pointer-events: none;
		}
	}
}
</style>