<template>
	<view class="page">
		<tabbar :cur="2"></tabbar>
		<u-modal :show="show" title="删除商品" content='确认要删除该订单吗' showCancelButton @confirm="confirmDel"
			@cancel="show=false"></u-modal>
		<view class="">
			<view class="car_item" v-for="(item,index) in list" :key="index">
				<view class="trash">
					<label class="checkbox">
						<radio class="small-radio" :checked="item.allChecked" @click="toggleAll(item)"
							color="#2979ff" />
						<text style="font-size:31rpx ; font-weight: 600;" class="name">{{item.name}}</text>
					</label>
					<u-icon name="trash" color="#2979ff" size="26" @click="goTrash(item)"></u-icon>
				</view>
				<view class="divider"></view>
				<view v-for="(item2,index2) in item.value" :key="index2" class="top">
					<label class="checkbox">
						<radio class="small-radio" :checked="item2.checked" @click="toggleItem(item, item2)"
							color="#2979ff" />
					</label>
					<image :src="item2.cover" mode=""></image>
					<view @click="showdetail(item2)"  class="right">
						<div class="choose">
							已选：
							<span v-for="(item3, index3) in item2.settingVals" :key="index3"
								:class="{'last-item': index3 === item2.settingVals.length - 1}">
								{{ item3.val }}<template v-if="index3 !== item2.settingVals.length - 1">,</template>
							</span>
						</div>
						<view class="price">
							<text v-if="item2.price == 0">师傅报价</text>
							<text v-else>{{item2.price}}元/台起</text>
							<u-number-box v-model="item2.num" :min="1">
								<template slot="minus">
									<view @click="minus(item2)"
										style="width: 70rpx;height: 60rpx;background-color: #ebecee;display: flex;justify-content: center;align-items: center;">
										<u-icon name="minus" color="#333" size="16"></u-icon>
									</view>
								</template>
								<template slot="plus">
									<view @click="plus(item2)"
										style="width: 70rpx;height: 60rpx;background-color: #ebecee;display: flex;justify-content: center;align-items: center;">
										<u-icon name="plus" color="#333" size="16"></u-icon>
									</view>
								</template>
							</u-number-box>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部结算栏 -->
		<view class="footer">
			<view class="footer-left">
				<label class="checkbox">
					<radio class="large-radio" :checked="isAllChecked" @click="toggleAllCheck" color="#2979ff" />
					<text>全选</text>
				</label>
			</view>
			<view class="footer-center">
				<text>合计：</text>
				<text class="total-price">{{totalPrice}}元</text>
			</view>
			<view class="footer-right" @click="goToOrder">
				去下单
			</view>
		</view>
	</view>
</template>

<script>
	import tabbar from "@/components/tabbar.vue"
	export default {
		components: {
			tabbar
		},
		data() {
			return {
				show: false,
				value: 1,
				list: [],
				id: '',
				selectedIds: []
			}
		},
		computed: {
			// 计算是否全选
			isAllChecked() {
				return this.list.length > 0 && this.list.every(item => item.allChecked);
			},
			// 计算总价
			totalPrice() {
				let total = 0;
				this.list.forEach(item => {
					item.value.forEach(subItem => {
						if (subItem.checked && subItem.price > 0) {
							total += subItem.price * subItem.num;
						}
					});
				});
				return total.toFixed(2);
			}
		},
		methods: {
				showdetail(item){
					console.log(item)
					
					uni.navigateTo({
						url:`../user/order_confirm?id=${item.id}&serviceId=${item.serviceId}`
					})
				},
			godetail(){
				uni.navigateTo({
					url:'../user/order_confirm'
				})
			},
			confirmDel() {
				this.show = false;
				console.log(this.selectedIds);
				this.$api.service.discar({
					ids: this.selectedIds
				}).then(res => {
					uni.showToast({
						icon: 'success',
						title: '删除成功'
					});
					setTimeout(() => {
						this.getList();
					}, 500);
				});
			},
			goTrash(item) {
				console.log(item);
				this.show = true;
				this.id = item.id;
				this.selectedIds = item.value.filter(subItem => subItem.checked).map(subItem => subItem.id);
				if (this.selectedIds.length === 0) {
					this.selectedIds = [item.id];
				}
			},
			plus(item) {
				this.$api.service.updatatocar({
					serviceId: item.serviceId,
					id: item.id,
					num: item.num+1
				}).then(() => {
					this.getList();
				});
			},
			minus(item) {
				if (item.num == 1) return;
				this.$api.service.updatatocar({
					serviceId: item.serviceId,
					id: item.id,
					num: item.num-1
				}).then(() => {
					this.getList();
				});
			},
			// 全选/取消全选单个分类
			toggleAll(item) {
				item.allChecked = !item.allChecked;
				item.value.forEach(child => {
					child.checked = item.allChecked;
				});
			},
			// 全选/取消全选所有商品
			toggleAllCheck() {
				const newStatus = !this.isAllChecked;
				this.list.forEach(item => {
					item.allChecked = newStatus;
					item.value.forEach(child => {
						child.checked = newStatus;
					});
				});
			},
			// 单选
			toggleItem(item, item2) {
				item2.checked = !item2.checked;
				// 检查是否所有子项都被选中
				item.allChecked = item.value.every(child => child.checked);
			},
			getList() {
				this.$api.service.seecar().then(res => {
					console.log(res);
					// 初始化选中状态
					this.list = res.data.map(group => {
						return {
							...group,
							allChecked: false,
							value: group.value.map(item => {
								return {
									...item,
									checked: false
								};
							})
						};
					});
				});
			},
			goDown(item) {
				uni.redirectTo({
					url: `../user/commodity_details?id=${item.serviceId}`
				});
			},
			// 去下单
			goToOrder() {
				const selectedItems = [];
				this.list.forEach(item => {
					item.value.forEach(subItem => {
						if (subItem.checked) {
							selectedItems.push({
								id: subItem.serviceId,
								num: subItem.num
							});
						}
					});
				});

				if (selectedItems.length === 0) {
					uni.showToast({
						title: '请选择商品',
						icon: 'none'
					});
					return;
				}
	uni.redirectTo({
					url: `../user/commodity_details?id=${item.serviceId}`
				});
				// 这里添加下单逻辑
				console.log('去下单', selectedItems);
				uni.showToast({
					title: '跳转下单页面',
					icon: 'none'
				});
			}
		},
		onLoad() {
			let token = uni.getStorageSync('token');
			console.log(111);
			if (!token || token == '') {
				return;
			} else {
				this.getList();
			}
		}
	}
</script>

<style scoped lang="scss">
	.page {
		background-color: #F8F8F8;
		height: 100vh;
		overflow: auto;
		padding: 40rpx 0;
		padding-bottom: 200rpx;

		.small-radio {
			transform: scale(0.8);
			transform-origin: center;
		}

		/* 调整最后一个 radio 为大尺寸 */
		.large-radio {
			transform: scale(1);
			transform-origin: center;
		}

		.car_item {
			margin: 0 auto;
			width: 686rpx;
			background: #FFFFFF;
			border-radius: 12rpx 12rpx 12rpx 12rpx;
			margin-bottom: 20rpx;
			padding: 0 20rpx;
			position: relative;

			.trash {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 20rpx 0;

				.checkbox {
					display: flex;
					align-items: center;

					.name {
						margin-left: 10rpx;
						font-size: 28rpx;
						color: #333;
					}
				}
			}

			.divider {
				height: 2rpx;
				background-color: #F2F3F6;
				margin: 0 -20rpx;
			}

			.top {
				padding: 36rpx 0;
				display: flex;
				align-items: center;
				border-bottom: 2rpx solid #F2F3F6;

				.checkbox {
					margin-right: 20rpx;
					display: flex;
					align-items: center;
				}

				image {
					width: 200rpx;
					height: 200rpx;
					margin-right: 20rpx;
				}

				.right {
					flex: 1;

					.name {
						font-size: 28rpx;
						font-weight: 500;
						color: #171717;
					}

					.choose {
						span {
							font-size: 24rpx;
							font-weight: 400;
							color: #ADADAD;
							margin-right: 4rpx; // 增加项之间的间距

							&.last-item {
								margin-right: 0; // 最后一项不需要右边距
							}
						}
					}

					.price {
						width: 100%;
						margin-top: 68rpx;
						font-size: 20rpx;
						font-weight: 500;
						color: #E72427;
						display: flex;
						justify-content: space-between;
						align-items: center;
					}
				}
			}
		}

		/* 底部结算栏样式 */
		.footer {
			position: fixed;
			bottom: 130rpx;
			left: 0;
			right: 0;
			height: 100rpx;
			background-color: #fff;
			display: flex;
			align-items: center;
			border-top: 1rpx solid #f2f2f2;
			padding: 0 60rpx;
			padding-right: 40rpx;
			box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);

			.footer-left {
				flex: 1;
				display: flex;
				align-items: center;

				.checkbox {
					display: flex;
					align-items: center;

					text {
						margin-left: 10rpx;
						font-size: 28rpx;
						color: #333;
					}
				}
			}

			.footer-center {
				flex: 2;
				text-align: right;
				padding-right: 30rpx;

				text {
					font-size: 28rpx;
					color: #333;
				}

				.total-price {
					font-size: 32rpx;
					font-weight: bold;
					color: #E72427;
				}
			}

			.footer-right {
				width: 200rpx;
				height: 80rpx;
				background-color: #2979ff;
				color: #fff;
				border-radius: 40rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 32rpx;
			}
		}
	}
</style>