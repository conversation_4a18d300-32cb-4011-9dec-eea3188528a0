<template>
	<view class="page">
		<u-modal :show="show" title="取消订单" content='确认取消该订单吗' showCancelButton @cancel="show= false" @confirm="confirm"></u-modal>
		<u-modal :show="afterSalesShow" title="申请售后" showCancelButton @cancel="afterSalesShow= false" @confirm="submitAfterSales">
			<view class="after-sales-input">
				<view class="">
					<textarea v-model="afterSalesValue" placeholder="请输入售后内容" style=" padding: 20rpx; border: 2rpx solid #E9E9E9; border-radius: 8rpx; writing-mode: horizontal-tb; text-align: left;"></textarea>
				</view>
			</view>
		</u-modal>
		<view  class="header" v-if="info.coachInfo">
			<view class="top" >
				<view class="left">
					<view class="" style="display: flex;align-items: center;">
						<view class="name">{{info.coachInfo.coachName}}</view>
						<view class="" style="background-color: #fac21f;color: #fff;width: fit-content;padding: 5rpx 10rpx;font-size: 24rpx;margin-left: 20rpx;border-radius: 6rpx;" v-if="info.coachInfo.label_name">{{info.coachInfo.label_name}}</view>
					</view>
					<view class="time">{{info.createTime}}</view>
				</view>
				<view class="right">
					<image :src="info.coachInfo.selfImg" mode=""></image>
				</view>
			</view>
			<view v-if="![-1, 1, -2, -3].includes(info.payType)" class="bott" @click="call">
				<view class="box"><uni-icons type="phone-filled" size="16" color="#fff"></uni-icons></view>
				<text>打电话给师傅</text>
			</view>
		</view>
		<view v-if="info.payType===7"  class="after-sales-btn" @click="openAfterSales">
			去售后
		</view>
		<view class="schedule">
			<u-steps current="4" direction="column">
				<u-steps-item :title="item.title" :desc="item.desc" v-for="(item,index) in list" :key="index">
					<view class="slot-icon" slot="icon">
						<view class="" style="border-radius: 50%;background-color:#00b26a;padding: 5rpx;">
							<u-icon name="checkbox-mark" color="#ffffff" size="14"></u-icon>
						</view>
					</view>
				</u-steps-item>
			</u-steps>
		</view>
		<view class="info" v-for="(item,index) in infoList" :key="index">
			<view class="title">{{item.title}}</view>
			<!-- 服务信息特殊处理，显示商品卡片布局 -->
			<view v-if="item.title === '服务信息'" class="service-info">
				<!-- 服务类型和师傅信息 -->
				<view class="info_item" v-for="(newItem,newIndex) in item.children.filter(child => child.isBasicInfo)" :key="newIndex">
					<view class="left">{{newItem.name}}</view>
					<view class="right">{{newItem.value}}</view>
				</view>

				<!-- 商品卡片列表 -->
				<view class="goods-list">
					<view class="goods-card" v-for="(goods, goodsIndex) in info.orderGoods" :key="goodsIndex">
						<view class="goods-main">
							<image class="goods-image" :src="goods.goodsCover" mode="aspectFill"></image>
							<view class="goods-content">
								<view class="goods-name">{{goods.goodsName}}</view>
								<view class="goods-details">
									<view class="detail-item" v-for="setting in goods.priceSetting" :key="setting.id">
										<text class="detail-text" v-if="setting.val && setting.val !== '' && setting.inputType !== 2">{{setting.problemDesc}}：{{setting.val}}</text>
									</view>
								</view>
							</view>
							<view class="goods-right">
								<view class="goods-price" v-if="goods.price > 0">¥{{goods.price}}</view>
								<view class="goods-num">x{{goods.num}}</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<!-- 其他信息正常显示 -->
			<view v-else class="info_item" v-for="(newItem,newIndex) in item.children" :key="newIndex">
				<view class="left">{{newItem.name}}</view>
				<view class="right">{{newItem.value}}</view>
			</view>
		</view>
		<view class="btn" v-if="info.payType<=1 && info.payType != -1" @click="cancelOrder">取消订单</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				show: false,
				afterSalesShow: false,
				afterSalesValue: '',
				info: {},
				list: [{
					title: '订单已生成',
					desc: '预定成功，将尽快为主人派单'
				}],
				infoList: [{
						title: '预约信息',
						children: []
					},
					{
						title: "服务信息",
						children: []
					},
					{
						title: "费用明细",
						children: []
					},
					{
						title: "优惠明细",
						children: []
					},
					{
						title: "下单明细",
						children: []
					}
				],
				id: ''
			}
		},
		methods: {
			confirm() {
				this.$api.service.cancelOrder({id: this.info.id}).then(res => {
					uni.showToast({
						icon: 'none',
						title: '取消成功'
					})
					uni.$emit('cancelOr')
					setTimeout(() => {
						uni.navigateBack()
					}, 1000)
				})
			},
			cancelOrder() {
				this.show = true
			},
			openAfterSales() {
				this.afterSalesShow = true
			},
			submitAfterSales() {
				if (!this.afterSalesValue.trim()) {
					uni.showToast({
						icon: 'none',
						title: '请输入售后内容'
					})
					return
				}
				// Here you would typically call an API to submit the after-sales request
				this.$api.service.submitAfterSales({
					orderId: this.id,
					remark: this.afterSalesValue
				}).then(res => {
					console.log(res)
					if(res.code==="-1"){
						uni.showToast({
							icon: 'none',
							title: res.msg
						})
					}else{
						uni.showToast({
							icon: 'none',
							title: '售后申请提交成功'
						})
						this.afterSalesShow = false
						this.afterSalesValue = ''
					}
					
				}).catch(err => {
					uni.showToast({
						icon: 'none',
						title: '提交失败，请重试'
					})
				})
			},
			call() {
				if (!this.info.coachInfo.mobile || this.info.coachInfo.mobile.includes('*')) {
				    uni.showToast({
				      title: '无法拨打电话，号码不可用',
				      icon: 'none'
				    });
				    return;
				  }
				uni.makePhoneCall({
					phoneNumber: this.info.coachInfo.mobile
				});
			},
			getInfo() {
				this.$api.service.orderdet(this.id).then(res => {
					console.log(res)
					this.info = res.data
					if (this.info.coachInfo) {
						this.list.push({
							title: '订单已派单',
							desc: `订单交给${this.info.coachInfo.coachName}，将督促师傅尽快跟您联系`
						})
					}
					if (this.info.payType == 7) {
						this.list.push({
							title: '订单完成',
							desc: `订单已完成`
						})
					} else if (this.info.payType == -1) {
						this.list.push({
							title: '订单取消',
							desc: `订单已取消`
						})
					}
					this.infoList[0].children = [{
							name: '预约时间',
							value: this.info.startTime
						},
						{
							name: '服务地址',
							value: this.info.addressInfo.address + this.info.addressInfo.addressInfo + this.info.addressInfo.houseNumber
						},
						// {
						// 	name: '预约服务',
						// 	value: this.info.orderGoods[0].goodsName
						// }
					]
					// 构建服务信息，只包含基本信息（服务类型、师傅信息）
					// 商品详情将在模板中直接渲染
					this.infoList[1].children = [
						{
							name: '服务类型',
							value: this.info.type == 0 ? '一口价' : '报价',
							isBasicInfo: true
						},
						{
							name: '服务师傅',
							value: this.info.coachInfo ? this.info.coachInfo.coachName : '',
							isBasicInfo: true
						}
					];
					this.infoList[2].children = [{
						name: '服务费用',
						value: this.info.payPrice + '元'
					}]
					this.infoList[3].children = [{
						name: '优惠券',
						value: this.info.couponInfo ? '-' + this.info.couponInfo.discount + '元' : '无'
					}]
					this.infoList[4].children = [{
							name: '订单号码',
							value: this.info.orderCode
						},
						{
							name: '下单时间',
							value: this.info.createTime 
						}
					]
				})
			}
		},
		onUnload() {
			let pageArr = getCurrentPages()
			let length = pageArr.length
			if (pageArr[length - 2].route == "/pages/order_success") {
				uni.navigateBack({
					delta: 9
				})
			}
		},
		onLoad(options) {
			this.id = options.id
			this.getInfo()
		}
	}
</script>

<style scoped lang="scss">
	.page {
		background-color: #f8f8f8;
		height: 100vh;
		overflow: auto;
		padding: 40rpx 30rpx;

		.header {
			width: 690rpx;
			height: 274rpx;
			background: #FFFFFF;
			border-radius: 24rpx 24rpx 24rpx 24rpx;
			padding: 28rpx 36rpx;

			.top {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding-bottom: 42rpx;
				border-bottom: 2rpx solid #E9E9E9;

				.left {
					.name {
						font-size: 32rpx;
						font-weight: 500;
						color: #333333;
					}

					.time {
						margin-top: 20rpx;
						font-size: 24rpx;
						font-weight: 400;
						color: #999999;
					}
				}

				.right {
					image {
						width: 80rpx;
						height: 80rpx;
						border-radius: 50%;
					}
				}
			}

			.bott {
				height: 100rpx;
				display: flex;
				align-items: center;

				text {
					margin-left: 20rpx;
					font-size: 28rpx;
					font-weight: 500;
					color: #2E80FE;
				}

				.box {
					width: 42rpx;
					height: 42rpx;
					background: #2E80FE;
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
				}
			}
		}

		.after-sales-btn {
			width: 686rpx;
			height: 88rpx;
			font-weight: 700;
			background: #FFFFFF;
			border-radius: 20rpx;
			font-size: 28rpx;
			font-weight: 400;
			color:#2E80FE;
			line-height: 88rpx;
			text-align: center;
			margin: 16rpx auto;
		}

		.after-sales-input {
			padding: 20rpx;
		}

		.schedule {
			width: 690rpx;
			padding: 32rpx 36rpx;
			background: #FFFFFF;
			border-radius: 24rpx 24rpx 24rpx 24rpx;

			.slot-icon {
				image {
					width: 32rpx;
					height: 32rpx;
				}
			}

			::v-deep .u-steps-item__line {
				background-color: #000 !important;
			}
		}

		.info {
			margin-top: 20rpx;
			width: 690rpx;
			background: #FFFFFF;
			border-radius: 24rpx 24rpx 24rpx 24rpx;
			padding: 28rpx 36rpx;

			.title {
				font-size: 32rpx;
				font-weight: 500;
				color: #333333;
				margin-bottom: 26rpx;
			}

			.info_item {
				display: flex;
				padding: 24rpx 0;
				align-items: center;
				justify-content: space-between;
				border-top: 2rpx solid #E9E9E9;

				.left {
					font-size: 28rpx;
					font-weight: 400;
					color: #ADADAD;
				}

				.right {
					max-width: 500rpx;
					font-size: 28rpx;
					font-weight: 400;
					color: #333333;
					text-align: right;
				}
			}

			.service-info {
				.goods-list {
					margin-top: 20rpx;
				}

				.goods-card {
					margin-bottom: 20rpx;
					padding: 20rpx 0;
					border-top: 2rpx solid #E9E9E9;

					&:last-child {
						margin-bottom: 0;
					}

					.goods-main {
						display: flex;
						align-items: flex-start;

						.goods-image {
							width: 120rpx;
							height: 120rpx;
							border-radius: 12rpx;
							flex-shrink: 0;
							margin-right: 20rpx;
						}

						.goods-content {
							flex: 1;
							min-width: 0;

							.goods-name {
								font-size: 32rpx;
								font-weight: 500;
								color: #333333;
								margin-bottom: 12rpx;
								line-height: 1.4;
							}

							.goods-details {
								.detail-item {
									margin-bottom: 8rpx;

									.detail-text {
										font-size: 24rpx;
										color: #666666;
										line-height: 1.3;
									}
								}
							}
						}

						.goods-right {
							flex-shrink: 0;
							text-align: right;
							display: flex;
							flex-direction: column;
							align-items: flex-end;
							justify-content: space-between;
							height: 120rpx;

							.goods-price {
								font-size: 32rpx;
								font-weight: 500;
								color: #FF4444;
								margin-bottom: 8rpx;
							}

							.goods-num {
								font-size: 28rpx;
								color: #999999;
							}
						}
					}
				}
			}
		}

		.btn {
			margin: 0 auto;
			margin-top: 40rpx;
			width: 686rpx;
			height: 88rpx;
			background: #2E80FE;
			border-radius: 44rpx 44rpx 44rpx 44rpx;
			font-size: 28rpx;
			font-weight: 400;
			color: #FFFFFF;
			line-height: 88rpx;
			text-align: center;
		}
	}
</style>